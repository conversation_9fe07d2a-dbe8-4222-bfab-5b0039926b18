import 'package:flutter/material.dart';
import 'package:hexacom_user/common/enums/footer_type_enum.dart';
import 'package:hexacom_user/common/models/config_model.dart';
import 'package:hexacom_user/common/widgets/bullet_point_text_widget.dart';
import 'package:hexacom_user/common/widgets/custom_app_bar_widget.dart';
import 'package:hexacom_user/common/widgets/footer_web_widget.dart';
import 'package:hexacom_user/common/widgets/no_data_screen.dart';
import 'package:hexacom_user/common/widgets/not_logged_in_screen.dart';
import 'package:hexacom_user/common/widgets/paginated_list_view.dart';
import 'package:hexacom_user/features/auth/providers/auth_provider.dart';
import 'package:hexacom_user/features/profile/providers/profile_provider.dart';
import 'package:hexacom_user/features/splash/providers/splash_provider.dart';
import 'package:hexacom_user/features/wallet/controllers/wallet_controller.dart';
import 'package:hexacom_user/features/wallet/widgets/transaction_shimmer.dart';
import 'package:hexacom_user/features/wallet/widgets/transaction_widget.dart';
import 'package:hexacom_user/features/wallet/widgets/wallet_history_title_widget.dart';
import 'package:hexacom_user/helper/responsive_helper.dart';
import 'package:hexacom_user/localization/language_constrants.dart';
import 'package:hexacom_user/utill/dimensions.dart';
import 'package:hexacom_user/utill/routes.dart';
import 'package:hexacom_user/utill/styles.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:provider/provider.dart';

import '../widgets/wallet_amount_widget.dart';

class WalletScreen extends StatefulWidget {
  final bool isBacButtonExist;
  const WalletScreen({super.key, this.isBacButtonExist = true});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  final tooltipController = JustTheController();
  final TextEditingController inputAmountController = TextEditingController();
  final FocusNode focusNode = FocusNode();
  final ScrollController scrollController = ScrollController();
  late bool _isLoggedIn;
  late final ConfigModel configModel;

  @override
  void initState() {

    final WalletProvider walletProvider = Provider.of<WalletProvider>(context, listen: false);
    _isLoggedIn = Provider.of<AuthProvider>(context, listen: false).isLoggedIn();
    configModel =  Provider.of<SplashProvider>(context,listen: false).configModel!;
    if(_isLoggedIn){
      Provider.of<ProfileProvider>(context, listen: false).getUserInfo();
      walletProvider.getTransactionList(1, isUpdate: false);
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _){
        if(Navigator.canPop(context) && !ResponsiveHelper.isWeb()){
          Navigator.pop(context);
        }else if(!didPop && !Navigator.canPop(context) ){
          RouteHelper.getMainRoute(context, action: RouteAction.pushNamedAndRemoveUntil);
        }
      },
      child: Scaffold(
          appBar: CustomAppBarWidget(
            title: getTranslated('wallet', context),
            onBackPressed: (){
              if(!Navigator.canPop(context)){
                RouteHelper.getMainRoute(context, action: RouteAction.pushNamedAndRemoveUntil);
              } else{
                Navigator.pop(context);
              }
            },
          ),
          resizeToAvoidBottomInset: false,
          /// Web Design
          body: configModel.customerSetupWalletEarning?.status == 1 ?
          _isLoggedIn ?
          ResponsiveHelper.isDesktop(context) ?
          SingleChildScrollView(
            child: Column(children: [
              const SizedBox(height: Dimensions.paddingSizeExtraLarge),

              SizedBox(width: Dimensions.webScreenWidth,
                child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Expanded(flex: 3,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: Dimensions.fontSizeThirty, vertical: 40),
                      decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(Dimensions.radiusSizeLarge),
                          border: Border.all(width: 1, color: Theme.of(context).hintColor.withValues(alpha: 0.05)),
                          boxShadow: [BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 5,
                            offset: Offset(0, 5),
                          )]
                      ),
                      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                        WalletAmountWidget(tooltipController: tooltipController, focusNode: focusNode, inputAmountController: inputAmountController),
                        SizedBox(height: Dimensions.paddingSizeLarge),

                        Padding(
                          padding: const EdgeInsets.all(Dimensions.paddingSizeDefault),
                          child: Column(crossAxisAlignment: CrossAxisAlignment.start,children: [

                            Text(getTranslated('how_to_use', context), style: rubikMedium.copyWith(
                              fontSize: Dimensions.fontSizeLarge, color: Theme.of(context).indicatorColor,
                            )),
                            SizedBox(height: Dimensions.paddingSizeSmall),

                            BulletPointTextWidget(text: getTranslated('you_can_get_the_wallet_balance_by_getting', context)),
                            SizedBox(height: Dimensions.paddingSizeSmall),

                            BulletPointTextWidget(text: getTranslated('you_can_use_the_wallet_balance_to_pay', context)),

                          ]),
                        ),
                      ]),
                    ),
                  ),
                  const SizedBox(width: Dimensions.paddingSizeExtraLarge),

                  Expanded(flex: 5,
                    child: Column(children: [
                      Padding(
                        padding: const EdgeInsets.only(right: Dimensions.paddingSizeSmall),
                        child: WalletHistoryTitleWidget(),
                      ),

                      SizedBox(height: size.height * 0.5,
                        child: Consumer<WalletProvider>(builder: (context, walletProvider, _) {
                          return Padding(
                            padding: const EdgeInsets.only(right: Dimensions.paddingSizeSmall),
                            child: (walletProvider.walletTransactionModel?.transaction?.isNotEmpty ?? false) ?  PaginatedListView(
                              scrollController: scrollController,
                              onPaginate: (int? offset) async => walletProvider.getTransactionList(offset ?? 1),
                              totalSize: walletProvider.walletTransactionModel?.totalSize,
                              offset: walletProvider.walletTransactionModel?.offset,
                              itemView: Expanded(
                                child: ListView.builder(
                                  controller: scrollController,
                                  shrinkWrap: true,
                                  itemCount: walletProvider.walletTransactionModel?.transaction?.length,
                                  itemBuilder: (ctx,index){
                                    return TransactionWidget(
                                      transaction: walletProvider.walletTransactionModel?.transaction?[index],
                                      isLastIndex: index + 1 == walletProvider.walletTransactionModel?.transaction?.length,
                                    );
                                  },
                                ),
                              ),
                            ) : (walletProvider.walletTransactionModel?.transaction?.isEmpty ?? false) ?
                            const NoDataScreen(title: 'no_transaction_history') : const TransactionShimmer(),
                          );
                        }),
                      ),
                    ]),
                  )
                ]),
              ),
            
              const FooterWebWidget(footerType: FooterType.nonSliver),
            ]),
          ) :
          RefreshIndicator(
            color: Theme.of(context).cardColor,
            backgroundColor: Theme.of(context).primaryColor,
            onRefresh: () async {
              Provider.of<WalletProvider>(context, listen: false).getTransactionList(1);
            },
            /// Mobile design
            child: CustomScrollView(controller: scrollController, slivers: [

              /// Wallet Amount Card
              if(!ResponsiveHelper.isDesktop(context))
                SliverToBoxAdapter(child: ResponsiveHelper.isDesktop(context) ? Center(child: SizedBox(
                  width: Dimensions.webScreenWidth,
                  child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                    Padding(
                      padding: const EdgeInsets.only(top: Dimensions.fontSizeThirty),
                      child: WalletAmountWidget(tooltipController: tooltipController, focusNode: focusNode, inputAmountController: inputAmountController),
                    ),
                  ]),
                )) : Padding(
                  padding: const EdgeInsets.all(Dimensions.paddingSizeLarge).copyWith(top: Dimensions.paddingSizeSmall, bottom: 0),
                  child: WalletAmountWidget(tooltipController: tooltipController, focusNode: focusNode, inputAmountController: inputAmountController),
                )),

              /// Wallet History Title
              if(!ResponsiveHelper.isDesktop(context))
                SliverPersistentHeader(pinned: true, delegate: _SliverDelegate(height: 70, child: Container(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: Column(children: [
                    const SizedBox(height: Dimensions.paddingSizeLarge),

                    ResponsiveHelper.isDesktop(context)
                        ? Center(child: SizedBox(width: Dimensions.webScreenWidth,child: WalletHistoryTitleWidget()))
                        : Padding(padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge), child: WalletHistoryTitleWidget()),

                    const SizedBox(height: Dimensions.paddingSizeSmall),
                  ]),
                ))),

              /// Wallet History list
              if(!ResponsiveHelper.isDesktop(context))
                SliverToBoxAdapter(
                  child: Consumer<WalletProvider>(
                      builder: (context, walletProvider, _) {
                        return (walletProvider.walletTransactionModel?.transaction?.isNotEmpty ?? false) ?  PaginatedListView(
                          scrollController: scrollController,
                          onPaginate: (int? offset) async => walletProvider.getTransactionList(offset ?? 1),
                          totalSize: walletProvider.walletTransactionModel?.totalSize,
                          offset: walletProvider.walletTransactionModel?.offset,
                          itemView: RepaintBoundary(
                            child: ListView.builder(
                              shrinkWrap: true,
                              padding: EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge),
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: walletProvider.walletTransactionModel?.transaction?.length,
                              itemBuilder: (ctx,index){
                                return TransactionWidget(
                                  transaction: walletProvider.walletTransactionModel?.transaction?[index],
                                  isLastIndex: index + 1 == walletProvider.walletTransactionModel?.transaction?.length,
                                );
                              },
                            ),
                          ),
                        ) : (walletProvider.walletTransactionModel?.transaction?.isEmpty ?? false) ?
                        const NoDataScreen(title: 'no_transaction_history', alignment: MainAxisAlignment.start) : Padding(
                          padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge),
                          child: const TransactionShimmer(),
                        );
                      }
                  ),
                ),
            ]),
          ) :
          NotLoggedInScreen() :
          NoDataScreen()
      ),
    );
  }
}



class _SliverDelegate extends SliverPersistentHeaderDelegate {
  Widget child;
  double height;
  _SliverDelegate({required this.child, this.height = 70});

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(_SliverDelegate oldDelegate) {
    return oldDelegate.maxExtent != height || oldDelegate.minExtent != height || child != oldDelegate.child;
  }
}







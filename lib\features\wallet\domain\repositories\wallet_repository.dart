import 'package:dio/dio.dart';
import 'package:hexacom_user/common/models/api_response_model.dart';
import 'package:hexacom_user/data/datasource/remote/dio/dio_client.dart';
import 'package:hexacom_user/data/datasource/remote/exception/api_error_handler.dart';
import 'package:hexacom_user/helper/date_converter_helper.dart';
import 'package:hexacom_user/utill/app_constants.dart';

class WalletRepository{
  final DioClient? dioClient;
  WalletRepository({required this.dioClient});


  Future getList({int? offset = 1, String? filterBy, DateTime? startDate, DateTime? endDate}) async{

    final Map<String, dynamic> queryParams = {
      'offset': offset,
      'limit': 10,
      if (filterBy != null && filterBy.isNotEmpty) 'filter_by': filterBy,
      if (startDate != null) 'start_date': DateConverterHelper.durationDateTime(startDate),
      if (endDate != null) 'end_date': DateConverterHelper.durationDateTime(endDate),
    };

    try {
      Response response = await dioClient!.get(AppConstants.walletTransactionList, queryParameters: queryParams);
      return ApiResponseModel.withSuccess(response);
    } catch (e) {
      return ApiResponseModel.withError(ApiErrorHandler.getMessage(e));
    }
  }


}
PODS:
  - AppAuth (1.7.5):
    - AppAuth/Core (= 1.7.5)
    - AppAuth/ExternalUserAgent (= 1.7.5)
  - AppAuth/Core (1.7.5)
  - AppAuth/ExternalUserAgent (1.7.5):
    - AppAuth/Core
  - audioplayers_darwin (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - FBAEMKit (16.3.1):
    - FBSDKCoreKit_Basics (= 16.3.1)
  - FBSDKCoreKit (16.3.1):
    - FBAEMKit (= 16.3.1)
    - FBSDKCoreKit_Basics (= 16.3.1)
  - FBSDKCoreKit_Basics (16.3.1)
  - FBSDKLoginKit (16.3.1):
    - FBSDKCoreKit (= 16.3.1)
  - Firebase/Auth (11.2.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.2.0)
  - Firebase/CoreOnly (11.2.0):
    - FirebaseCore (= 11.2.0)
  - Firebase/Crashlytics (11.2.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.2.0)
  - Firebase/Messaging (11.2.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.2.0)
  - firebase_auth (5.3.1):
    - Firebase/Auth (= 11.2.0)
    - firebase_core
    - Flutter
  - firebase_core (3.6.0):
    - Firebase/CoreOnly (= 11.2.0)
    - Flutter
  - firebase_crashlytics (4.0.3):
    - Firebase/Crashlytics (= 11.2.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.0.3):
    - Firebase/Messaging (= 11.2.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheckInterop (11.3.0)
  - FirebaseAuth (11.2.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (~> 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.3.0)
  - FirebaseCore (11.2.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.3.0):
    - FirebaseCore (~> 11.0)
  - FirebaseCoreInternal (11.3.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.2.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.3.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.2.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.3.0)
  - FirebaseSessions (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_facebook_auth (6.0.4):
    - FBSDKLoginKit (~> 16.3.1)
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - OrderedSet (5.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RecaptchaInterop (100.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.47.0):
    - sqlite3/common (= 3.47.0)
  - sqlite3/common (3.47.0)
  - sqlite3/dbstatvtab (3.47.0):
    - sqlite3/common
  - sqlite3/fts5 (3.47.0):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.47.0):
    - sqlite3/common
  - sqlite3/rtree (3.47.0):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - sqlite3 (~> 3.47.0)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - Google-Maps-iOS-Utils
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - sqlite3

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  AppAuth: 501c04eda8a8d11f179dbe8637b7a91bb7e5d2fa
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  connectivity_plus: ddd7f30999e1faaef5967c23d5b6d503d10434db
  FBAEMKit: 6c7b5eb77c96861bb59e040842c6e55bf39512ce
  FBSDKCoreKit: 5e4dd478947ab1bcc887e8cfadeae0727af1a942
  FBSDKCoreKit_Basics: cd7b5f5d1e8868c26706917919d058999ca672c3
  FBSDKLoginKit: 572cca0bc6c90067ef197187697cb3b584310c52
  Firebase: 98e6bf5278170668a7983e12971a66b2cd57fc8c
  firebase_auth: 0c77e299a8f2d1c74d1b1f6b78b3d4d802c19f47
  firebase_core: 2bedc3136ec7c7b8561c6123ed0239387b53f2af
  firebase_crashlytics: 7c9d66993e56f8d762fd270ea00c14e36bcb7aad
  firebase_messaging: de006c66d363974ef2f274fa8bfa2071e7482a77
  FirebaseAppCheckInterop: 7789a8adfb09e905ce02a76540b94b059029ea81
  FirebaseAuth: 2a198b8cdbbbd457f08d74df7040feb0a0e7777a
  FirebaseAuthInterop: c453b7ba7c49b88b2f519bb8d2e29edf7ada4a2a
  FirebaseCore: a282032ae9295c795714ded2ec9c522fc237f8da
  FirebaseCoreExtension: 30bb063476ef66cd46925243d64ad8b2c8ac3264
  FirebaseCoreInternal: ac26d09a70c730e497936430af4e60fb0c68ec4e
  FirebaseCrashlytics: cfc69af5b53565dc6a5e563788809b5778ac4eac
  FirebaseInstallations: 58cf94dabf1e2bb2fa87725a9be5c2249171cda0
  FirebaseMessaging: c9ec7b90c399c7a6100297e9d16f8a27fc7f7152
  FirebaseRemoteConfigInterop: c3a5c31b3c22079f41ba1dc645df889d9ce38cb9
  FirebaseSessions: 655ff17f3cc1a635cbdc2d69b953878001f9e25b
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: c8700ab1770f3d8e5e7456220e4f3bbcdb831454
  flutter_inappwebview_ios: 97215cf7d4677db55df76782dbd2930c5e1c1ea0
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  geocoding_ios: d7460f56e80e118d57678efe5c2cdc888739ff18
  geolocator_apple: 6cbaf322953988e009e5ecb481f07efece75c450
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  google_sign_in_ios: 07375bfbf2620bc93a602c0e27160d6afc6ead38
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  pointer_interceptor_ios: 508241697ff0947f853c061945a8b822463947c1
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  sqlite3: 0aa20658a9b238a3b1ff7175eb7bdd863b0ab4fd
  sqlite3_flutter_libs: b55ef23cfafea5318ae5081e0bf3fbbce8417c94
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 775997f741c536251164e3eacf6e34abf2eb7a17

COCOAPODS: 1.15.2

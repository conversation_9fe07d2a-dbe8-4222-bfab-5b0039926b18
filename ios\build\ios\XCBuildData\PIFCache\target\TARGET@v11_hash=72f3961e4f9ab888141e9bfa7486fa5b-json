{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fa44b6dd61b83912130a8800366a03bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc2e72882bf77e3e5ac0ef2803689502", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba25e183cfb15f5542b0dd80b92af329", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ae039069321fc7f3cc1ac77369d0d961", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba25e183cfb15f5542b0dd80b92af329", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d4ea330ac5e66f124254361aac78feb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986de21922f5035ebb86a7a8dce3778b67", "guid": "bfdfe7dc352907fc980b868725387e98a6f7ce8b620209e395aa879820fb44c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b012b9d40f09fee443c7714640cdc6", "guid": "bfdfe7dc352907fc980b868725387e981c9ad3f8085deaca9ef2f6d26997ab17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f47e6e0eb8d0a81756ee28e529ba564a", "guid": "bfdfe7dc352907fc980b868725387e98c47501d102d31805b5e2534266595315", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b4b5d91692be9164f6a047d2115afc4", "guid": "bfdfe7dc352907fc980b868725387e983a56c057b863db7a6a6fc831038f8129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983caa23d6d7b04a18ca6d10eaea4c4d0b", "guid": "bfdfe7dc352907fc980b868725387e98a56e2b1c3cf2248dd135639da23e2693", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf80b4d58a0514e5dc6f0b884f49243e", "guid": "bfdfe7dc352907fc980b868725387e98e07c96530e0e40ebeab7fe74b5a73ad6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987070a34337df942073f8b6f39d3e9dcf", "guid": "bfdfe7dc352907fc980b868725387e98905cee5dab88473b1856a1e3873e029b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859005d9b2e14cf2e6afff0f3ea4d931a", "guid": "bfdfe7dc352907fc980b868725387e98d2387220de5e4c530b088b631985afdf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983dc6945c09512dc280ff477e6a055486", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9800bdf432b5de00be7470e321ccd5ba4c", "guid": "bfdfe7dc352907fc980b868725387e982e4c5611f663e94b361b05075d3011cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f8f5de40e73e6f95c6fb51108b63565", "guid": "bfdfe7dc352907fc980b868725387e98ad11deacfe9774449f43fe9d59f69393"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814a5c96c7e90d752d5060ab51cc8f7ab", "guid": "bfdfe7dc352907fc980b868725387e9862c02dda001b6855b10137a50c9befb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d78f42c26154c655c05aa19fdcb5aa6e", "guid": "bfdfe7dc352907fc980b868725387e987c077340da40e4e62317f0a381aec27f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ffef5d13cf4236b161256d53794f536", "guid": "bfdfe7dc352907fc980b868725387e98848128b0939059925b3241d02cbee94e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820884f85cd57a10e1e890346880831d7", "guid": "bfdfe7dc352907fc980b868725387e989ea0e7f8f06bb6e6278d52c21fa699f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa03b8a07eefe8d035c9f0c17a6ef6bb", "guid": "bfdfe7dc352907fc980b868725387e981f2f13dcbe5051451976c9827fb784d4"}], "guid": "bfdfe7dc352907fc980b868725387e988609ddafb90352c23b89a6c96ed9b16f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98e9b83fccdb550a9ba519f430dc704c93"}], "guid": "bfdfe7dc352907fc980b868725387e98ddfc4316b232572929d9ca579ec0c94e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98117c12e2cce513e0ed72e47ea361ea89", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e983788d8769c821650606514be955fca93", "name": "firebase_auth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e984496a3f7661d89567ff8250961054e8f", "name": "firebase_auth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
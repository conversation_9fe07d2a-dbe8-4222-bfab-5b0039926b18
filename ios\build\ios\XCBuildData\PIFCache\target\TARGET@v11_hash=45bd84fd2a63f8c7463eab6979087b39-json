{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bbba59bd09867a1f41274003a836250", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9821bf0f9f2e6cfb70e4cc57aa0f2f410b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a2b5f5fc3e9ff28be478bf0bf7e6214", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892dc00a43091ec79eab3595b54af4926", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a2b5f5fc3e9ff28be478bf0bf7e6214", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980bb133b6abb84071a49f6318d46dca4e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823fd2921567cf05478f8b49b1096208a", "guid": "bfdfe7dc352907fc980b868725387e9852b94c0deae686572f5b2ae15b65602a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0c6c9931bd5716a3a38331ac80f0450", "guid": "bfdfe7dc352907fc980b868725387e98b1644aea2fb6fe097d98e7b2af756456", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7cad490aa8f752e5b516f5ae82b139a", "guid": "bfdfe7dc352907fc980b868725387e989608fe206fe6806204743b95d193c84c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aaed020946abc93f54c79fe55bf9c10", "guid": "bfdfe7dc352907fc980b868725387e9839db164b7dfb916d01407ae20a116b4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984086c4b46bb81074a4b57933306a15f5", "guid": "bfdfe7dc352907fc980b868725387e98ad50231e98c7de96d11e4c9200122d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d84555f855211ff3a30802ce7f2cc3de", "guid": "bfdfe7dc352907fc980b868725387e98309a674776b85c4cc97652496137d3fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc71054cd0908f210b4978d441bd906", "guid": "bfdfe7dc352907fc980b868725387e98913b4774553f1aeebe2ec9013998285c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc5e13fde0a3476da9960a4874161a8a", "guid": "bfdfe7dc352907fc980b868725387e9873410b6236466679872a7a8913d77b3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cb6fee1525ff81bc690523f54d36cba", "guid": "bfdfe7dc352907fc980b868725387e98cd42f88db135160046e3a9d455dc14a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e04dc9740cd74e56445976d7a745ae6", "guid": "bfdfe7dc352907fc980b868725387e98931017bd74dfcf433913d1fe4154dea2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f27e8d5d5a36ecb54d4b3f57c13d0dc1", "guid": "bfdfe7dc352907fc980b868725387e98ea7351accff37a402e1bc9461cfc57fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98012b612aea4e2ab870ac7a722d13e2a8", "guid": "bfdfe7dc352907fc980b868725387e980e8436f41424f2fa46605e265800d4a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e504967b1fef2136c80e19909b21e236", "guid": "bfdfe7dc352907fc980b868725387e98f32aaeb962850515734bf8513a1b8ea5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98130c0cea2852d30c6098232983e099e7", "guid": "bfdfe7dc352907fc980b868725387e98a85c1c926af86e46e289297d0cfe2754"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849a965d1a9e5f6f07d201a527afb1942", "guid": "bfdfe7dc352907fc980b868725387e9828c9584a4a41d533ecfcfcdb5bf42055"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f91d20cad8a41a5b99c5a9a83ce2bb8", "guid": "bfdfe7dc352907fc980b868725387e9811fd5c27572f2e446b03050106b9fad7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0bf2136623fab66361ebe919d31d4b3", "guid": "bfdfe7dc352907fc980b868725387e98cf5317b28a205373c890970fd8ea982f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e69ce6c226a4701465f0273857d35478", "guid": "bfdfe7dc352907fc980b868725387e989d80ec63d1c4ab16e5f3bf94729c76f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891bc29161d4280c37b7fa75867f062c3", "guid": "bfdfe7dc352907fc980b868725387e9868855dfc93d06cee500de6debfe5b66b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884880222c67cf041b0d229d3e6052486", "guid": "bfdfe7dc352907fc980b868725387e983d7270960fc5da0d4b503fa5fbe2a1ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800eaa3ac1ff7d9c6369aa92252ce3908", "guid": "bfdfe7dc352907fc980b868725387e985f16d282be44ad20ce93c981af3aa5a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893215396acfe45c6e5279f88e8e608ab", "guid": "bfdfe7dc352907fc980b868725387e9801c258e750921dee2403eaf69e6b30af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98961e55b472e724ffb0534f0b06be0b9e", "guid": "bfdfe7dc352907fc980b868725387e98c300b43a234136b1c19edfd8353ea57e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984b807c8c6c258778a75e37e530a28b44", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c007a16db41cc5c2d90425d53c6436f1", "guid": "bfdfe7dc352907fc980b868725387e983cab19bef47277107ee4ec04db944a2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891459a13e772a576c811a7349a7a842e", "guid": "bfdfe7dc352907fc980b868725387e987efa59390d374cf531de9f76085f3090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a711d4a740fabaf439332aa7262b380c", "guid": "bfdfe7dc352907fc980b868725387e9888296b2dbc841e9f5cc65cb9abf1214a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac76ef5b246101f08e994246459c46e", "guid": "bfdfe7dc352907fc980b868725387e98946d864a2786adc08b48e6fcc847b1e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c90077601c58cf5f0f7a45b9fefde77a", "guid": "bfdfe7dc352907fc980b868725387e98cfb19fb028631753d1fbbcbac42562de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda63fd9436ae6181a6e4262378c2d67", "guid": "bfdfe7dc352907fc980b868725387e98173dd9b562eb8e7e8fa8658171da8f38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e01ad6d01a54a960a9fa6b8a99792298", "guid": "bfdfe7dc352907fc980b868725387e988b141ad4b21d90e36bbd01e453f493d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd08c585111556052e2b9e37d2df91e", "guid": "bfdfe7dc352907fc980b868725387e98dee085d3c29503755da1da1ff88bc124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ea22f85ed6865466fc65a4c8286109", "guid": "bfdfe7dc352907fc980b868725387e98a1af31513781a224dedd3671d18d73be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec4ae555162ff346c67edb5cd1ee302", "guid": "bfdfe7dc352907fc980b868725387e9816e4bbd58720f17b84137d48d6e687af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c9d2dff78bc1eed0075b7c656fd1c60", "guid": "bfdfe7dc352907fc980b868725387e988082c461c58efda6c551d9aaac2b3a02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fefcbfe93fa51304365522e4007bf817", "guid": "bfdfe7dc352907fc980b868725387e984b5902180e84ca98bc18c3a4859c519e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebcfa94128f072a50881e88231b19610", "guid": "bfdfe7dc352907fc980b868725387e9841b95e1ad8f828cbf855856e7fcf90b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d486c3c45061d50cdbbb38938de910b8", "guid": "bfdfe7dc352907fc980b868725387e9810103d24c11d7734bb9c70d80188f614"}], "guid": "bfdfe7dc352907fc980b868725387e987c7b735a425764be8dc7795525d85d2c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98afd2aebd2ba31722a2aab30da8c0f1c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec82dece765d451054cf9546cb2f0d4", "guid": "bfdfe7dc352907fc980b868725387e9864c35a8fb58a060dc028c3239c2b6953"}], "guid": "bfdfe7dc352907fc980b868725387e98d655db8354343b1aad2401f213e6d3b4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989503a68eac82d21bce30e1850cd6de56", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98903292f8ffcc7b80f5fb79893ed08f6f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
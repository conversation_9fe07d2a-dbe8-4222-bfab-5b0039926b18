{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b04ea652c4af8419a3d6e539169347d6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98124d05d95b0a88b50ba23971f882cc63", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859335a3713f807978a37f7180054238e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e60d334a0636be678d24ed155f17e68", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859335a3713f807978a37f7180054238e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850968fc64e545db8702a9275a14bb5b9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b3ae730c7c2ee3a41201a4a793bad7", "guid": "bfdfe7dc352907fc980b868725387e985e35902722fa51253ddf7f2db938da97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852e4561fa095cfa3058b5b51f2550687", "guid": "bfdfe7dc352907fc980b868725387e98e2a5599208a26afb7b4039e4959ce952", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9666793837c0e8e1357928624488b0c", "guid": "bfdfe7dc352907fc980b868725387e9818e03c9fea3edd75213a07e35449a5d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3edc1c55fc32d25e689fa3f69586dcb", "guid": "bfdfe7dc352907fc980b868725387e982fcc9c98e526c9cbb6f93f38af4878c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806d4133099cfd0f316f76e9bb20d9546", "guid": "bfdfe7dc352907fc980b868725387e985a06984f199f6f814ab15b85862e5780", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba989d1e8dcc802aa89ba097976bc345", "guid": "bfdfe7dc352907fc980b868725387e98b75dd9155907ec0a292fed4dbf884b11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d99bbef1cd1ef9253101594d57451157", "guid": "bfdfe7dc352907fc980b868725387e9857d87acb068a02d7430337d15f41dd58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866e4ae69dc2dc08f5f3e6f57966b698f", "guid": "bfdfe7dc352907fc980b868725387e98a569399f3c14881bd865c6a6b05e1149", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fb3ec563a78e3ba9667cdc5bbd5ba76", "guid": "bfdfe7dc352907fc980b868725387e982db2d4fa4d12eafecb2357bf794ec655", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98001b9c71d3c3129b41bdbcad4395ca9b", "guid": "bfdfe7dc352907fc980b868725387e98654aa1e5c08d16b054ddea2e799ac5bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ba2dce8dc46366dc795ac15e310954", "guid": "bfdfe7dc352907fc980b868725387e98f9a124058623e1856461786cf30173ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842ed8f93dc43b5b6d14e019d5aeddb3b", "guid": "bfdfe7dc352907fc980b868725387e987e71d97aab87d502f0c4a0938a1aaf1f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9851198fae0d8905fb79f391da70d27b9a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869fc1d333c5601370ecad17200063cf8", "guid": "bfdfe7dc352907fc980b868725387e9806ca13c36fada04737fab64394e4a672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5da4323218dff9554b025f3d5eb7f36", "guid": "bfdfe7dc352907fc980b868725387e98a0d16a409aaa316c68cd5d46e128cde0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801e46c9cd88e68fdf4bcacf5c95918f3", "guid": "bfdfe7dc352907fc980b868725387e9892fea007e276457a57031772f309c045"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ba1affec3aca974d949c9b7d8718e2", "guid": "bfdfe7dc352907fc980b868725387e98941f3cb1af77636cae41b58dc3b1c641"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff3865e209bcb06194b0af0ccc0a880", "guid": "bfdfe7dc352907fc980b868725387e987927569a5f7f57907c53705d324cc3d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abf0035f3942cac37042d82acb524a80", "guid": "bfdfe7dc352907fc980b868725387e987e33ea7e3016c74cabb61c94a8f47adf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984242939550c537ed41a2016e5ad19b08", "guid": "bfdfe7dc352907fc980b868725387e98a21b8b6943c8da0211363f50f88fad92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d8c0f2e9f43e6612717a80c30b534c0", "guid": "bfdfe7dc352907fc980b868725387e98fc72df5bd371ebe39666139682dfd6eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f846c3f6bb27340183e7ea10679b315f", "guid": "bfdfe7dc352907fc980b868725387e9828bf74086eea469280a9043214d02b81"}], "guid": "bfdfe7dc352907fc980b868725387e98c53cda76c6714cc99d33f8d438858f07", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98cdd7430e01d92244c5d5c3d4bd77581d"}], "guid": "bfdfe7dc352907fc980b868725387e98e25737a103f495bf6b667e8f6f32d4bd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9804c93d896fd7e7654fa5c52570948a90", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e9868af60886bbf96f1bb9951e1a108aa20", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818de625d87b991e29200cc47b43488c9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983acee0263ee80cf24be6e20e3fe1d937", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4b286ab63ebb13a585b530b870966f5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3c1f856451a44dd2238bb8e03f629a8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4b286ab63ebb13a585b530b870966f5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981496f826cc976bf124c3e2cdf403a9f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98215f555a911f03e4ce6ab2e253d43f20", "guid": "bfdfe7dc352907fc980b868725387e984b4e2b2d38ceb40598d0629a6a29a998", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1fa654169f9904269a72b4dcb9e850c", "guid": "bfdfe7dc352907fc980b868725387e983e0f4f5b863174fcb32043e87b8e66a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821393a60328992a4ca542f03a1a5cd04", "guid": "bfdfe7dc352907fc980b868725387e98dc580796952acfb7fbf0fded35f54e67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d95d9c37073e706bd675f7cf83e573a4", "guid": "bfdfe7dc352907fc980b868725387e98d22586e69bf2aef8b039891bcc650665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea2029a86d011a749ce0ce271b4ac265", "guid": "bfdfe7dc352907fc980b868725387e9865ebbb9e2407b80c90fed1db89b340e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ec390a50040b07676a9f2a605681e6a", "guid": "bfdfe7dc352907fc980b868725387e9899cdbb3f5c2a220155a76e55140322ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861e445f22fd5df6f32c7cfcbc98b24ec", "guid": "bfdfe7dc352907fc980b868725387e98fd3712aa9d01e1fc775bfabbb3600693", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa5ef8a384b67bbd0b696b25007b9384", "guid": "bfdfe7dc352907fc980b868725387e98850ddc5ee2cbcd07af827524c7dfb78e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe7e4a71e665e6641080e04590e3db4", "guid": "bfdfe7dc352907fc980b868725387e98036e607cec0f7f2b6292d3448d94d705", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f183532a8f77e53138f0b7d97ff9acc8", "guid": "bfdfe7dc352907fc980b868725387e98fa7d18e838630224541f4a32f8fe3d79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cafd21d38399b87d7f7f43d4af7d64f", "guid": "bfdfe7dc352907fc980b868725387e98d20f516538f062edb154dff2e9e72326", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b4b5df1609deacff0ed5b6b83868c2", "guid": "bfdfe7dc352907fc980b868725387e9863adc356814f9a68bae9d10e183d3dbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b9020068522239bea633efa8bdb7059", "guid": "bfdfe7dc352907fc980b868725387e98a562d3226230ecc9e150571e6a8ca86a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fab60553079f8e172fc01375aa27b0d6", "guid": "bfdfe7dc352907fc980b868725387e986a89fa83a23fb0ba50e0aaa9890b3cb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d022bf22b6ee31a9922ffae143c57c5", "guid": "bfdfe7dc352907fc980b868725387e98c7a4d2f624029d7e36489918cfb486d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801fc1a7b874cdb7356168bb30d873f2e", "guid": "bfdfe7dc352907fc980b868725387e98840c991519783c8bc41b731ef77fbf43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866a1647ca9cbb04576c74916d365b49f", "guid": "bfdfe7dc352907fc980b868725387e98b6ea7a37cc9b1b085cc626894408a980", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bf564ff1975ff472a8bc2c91f2bfcf9f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ad650c7728311d03d919ad3899f3cb61", "guid": "bfdfe7dc352907fc980b868725387e98fc2aeeff30372a8e9382bd9424da5e58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a6118a7cea6bc6205ac3841cd3eab4f", "guid": "bfdfe7dc352907fc980b868725387e98ba368f5b46b86ed2f9356bbb228ec697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad2b05f919e463398828c9e1c091373a", "guid": "bfdfe7dc352907fc980b868725387e98a07f46006e0e0b13acf0b1712cc73130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3364b4888fb91d4e6ee6d2e035ac645", "guid": "bfdfe7dc352907fc980b868725387e982608d167b16e3c511c909caa418e1af1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6c150f721e2c2e7d2edd25a92a81205", "guid": "bfdfe7dc352907fc980b868725387e98185a926dec138453c5fabdc29d0c9a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986496c8556538e0202335834a675545d0", "guid": "bfdfe7dc352907fc980b868725387e9881210550520b1ee3bfa30fce3d413653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2f9a9bb27a3bd097917562f443268b3", "guid": "bfdfe7dc352907fc980b868725387e989db863b76450c07e6a3efeefea5d239e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e811421dc53cf5ca2ef7f1dbe11bd598", "guid": "bfdfe7dc352907fc980b868725387e986da211b77f3869a367c7cc59fd32c453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988697813ba097ea8ed0c19cc3285bce6f", "guid": "bfdfe7dc352907fc980b868725387e981d7eb2fc908b56d891d52aed5a4a5892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fde38a1900833146666e853d49bba94", "guid": "bfdfe7dc352907fc980b868725387e98f50096c22f8337a29ee522ddda53a055"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830782e5403ab46636354504cab5d149c", "guid": "bfdfe7dc352907fc980b868725387e9842aab6e6820437225615089c83902796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987516d8940851c2d38f8d46e2e7b76bd3", "guid": "bfdfe7dc352907fc980b868725387e9857aea391777027b04b5707666b521569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c9a31b7240e5f0bf9575bf353610c37", "guid": "bfdfe7dc352907fc980b868725387e9881ead7d5ee06c80b6d2e153309173273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2dac33fd0c46107db83848fcf668291", "guid": "bfdfe7dc352907fc980b868725387e98eaaa226cb9428e050cb7f41b2f6c2bd1"}], "guid": "bfdfe7dc352907fc980b868725387e981b2b4abaaa263ff11fa795c21ec7f9d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e9850046155a8d9eab9c34f139cf05eb31e"}], "guid": "bfdfe7dc352907fc980b868725387e9800faf1016cc785048ac6ced913c4cff8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc5dfdf9349a221f5258db13a60fd150", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98651b31e0272f628eba1fc39908fbac59", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
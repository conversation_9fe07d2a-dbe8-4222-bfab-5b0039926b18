import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hexacom_user/common/widgets/custom_asset_image_widget.dart';
import 'package:hexacom_user/features/wallet/widgets/wallet_card_widget.dart';
import 'package:hexacom_user/helper/responsive_helper.dart';
import 'package:hexacom_user/provider/theme_provider.dart';
import 'package:hexacom_user/utill/dimensions.dart';
import 'package:hexacom_user/utill/images.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:provider/provider.dart';

class WalletAmountWidget extends StatelessWidget {
  const WalletAmountWidget({
    super.key,
    required this.tooltipController,
    required this.focusNode,
    required this.inputAmountController,
  });

  final JustTheController tooltipController;
  final FocusNode focusNode;
  final TextEditingController inputAmountController;

  @override
  Widget build(BuildContext context) {
    final double widthSize = MediaQuery.of(context).size.width;

    return Stack(alignment: Alignment.centerLeft,children: [
      Container(
        height: !ResponsiveHelper.isDesktop(context) ? (widthSize * 0.33) : (widthSize * 0.07),
        width: !ResponsiveHelper.isDesktop(context) ? widthSize : null,
        padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge),
        decoration: BoxDecoration(
          color: Provider.of<ThemeProvider>(context, listen: false).darkTheme
              ? Theme.of(context).cardColor
              : Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(Dimensions.radiusSizeLarge),
        ),
        child: WalletCardWidget(
          tooltipController: tooltipController, focusNode: focusNode,
          inputAmountController: inputAmountController,
        ),
      ),

      Positioned(right: Dimensions.paddingSizeExtraSmall, bottom: -6, child: Transform.rotate(angle: 0.3, child: CustomAssetImageWidget(
        Images.walletIcon, height: 50, width: 50, color: Theme.of(context).cardColor,
      ))),

    ]);
  }
}
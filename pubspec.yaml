name: hexacom_user
description: A new Flutter restaurant application.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 1.0.0+0

environment:
  sdk: '>=3.4.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8
  get_it: ^8.0.3
  http: ^0.13.6
  provider: ^6.1.2
  shared_preferences: ^2.2.3
  dio: ^5.4.3+1
  image_picker: ^1.1.0
  pin_code_fields: ^8.0.1
  google_maps_flutter_web: ^0.5.6+2
  google_maps_flutter: ^2.6.1
  google_maps_webservice: ^0.0.20-nullsafety.5
  geocoding: ^3.0.0
  geolocator: ^13.0.4
  shimmer_animation: ^2.1.0+1
  shimmer: ^3.0.0
  url_launcher: ^6.2.6
  url_launcher_web: ^2.3.1
  firebase_core: ^3.5.0
  firebase_messaging: ^15.2.5
  flutter_local_notifications: ^18.0.1
  path_provider: ^2.1.3
  connectivity_plus: ^6.0.3
  photo_view: ^0.15.0
  carousel_slider: ^5.0.0
  universal_html: ^2.2.4
  image: ^4.1.7
  flutter_widget_from_html_core: ^0.16.0
  country_code_picker: ^3.0.0
  flutter_typeahead: ^5.2.0
  permission_handler: ^11.3.1
  intl: any
  flutter_inappwebview: ^6.0.0
  audioplayers: ^6.0.0
  flutter_facebook_auth: ^6.0.4
  google_sign_in: ^6.2.1
  simple_speed_dial: ^0.1.7
  cached_network_image: ^3.3.1
  dotted_border: ^2.1.0
  flutter_svg: ^2.0.9
  flutter_xlider: ^3.5.0
  firebase_crashlytics: ^4.3.5
  firebase_auth: ^5.3.0
  flutter_staggered_grid_view: ^0.7.0
  phone_numbers_parser: ^9.0.0
  go_router: ^14.8.1
  url_strategy: ^0.3.0
  dropdown_button2: ^2.3.9
  sign_in_with_apple: ^7.0.1
  drift: ^2.19.0
  drift_flutter: ^0.2.4
  scroll_to_index: ^3.0.1
  just_the_tooltip: ^0.0.12
  syncfusion_flutter_datepicker: ^28.2.4
  cached_network_image_platform_interface:

dependency_overrides:
  flutter_inappwebview_ios:
    git:
      url: https://github.com/andychucs/flutter_inappwebview
      ref: master
      path: flutter_inappwebview_ios

dev_dependencies:
  flutter_lints: ^5.0.0
  drift_dev: ^2.19.0
  build_runner: ^2.4.13
  flutter_test:
    sdk: flutter


flutter:
  uses-material-design: true

  assets:
    - assets/icon/
    - assets/image/
    - assets/svg/
    - assets/language/
    - assets/

  fonts:
    - family: Exo
      fonts:
        - asset: assets/fonts/Exo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Exo-Medium.ttf
          weight: 600
        - asset: assets/fonts/Exo-SemiBold.ttf
          weight: 500
        - asset: assets/fonts/Exo-Bold.ttf
          weight: 700
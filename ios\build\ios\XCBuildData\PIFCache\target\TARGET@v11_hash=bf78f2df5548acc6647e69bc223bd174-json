{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b153bb026b86612cef6d7458c9684c3c", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_sign_in_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_sign_in_ios", "INFOPLIST_FILE": "Target Support Files/google_sign_in_ios/ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "google_sign_in_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98259d8fe7ffe01a9a0813b612e12d254a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98904f57856f6f647f36e4d25cb2809341", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_sign_in_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_sign_in_ios", "INFOPLIST_FILE": "Target Support Files/google_sign_in_ios/ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "google_sign_in_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982e6bcf2874dbe9de9bd7980b427fb031", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98904f57856f6f647f36e4d25cb2809341", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_sign_in_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_sign_in_ios", "INFOPLIST_FILE": "Target Support Files/google_sign_in_ios/ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "google_sign_in_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a21d6dba89849e206c401aeb8c2f884c", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98bb26e30ca91245de6bba419e981f3ebc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9878ed28cf75d14f9a51b56c6c19849887", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc95385bedc73dda13baea2b04d59503", "guid": "bfdfe7dc352907fc980b868725387e982fcff576974bb5bca7a0f00feecc55ca"}], "guid": "bfdfe7dc352907fc980b868725387e988ed1b48b63c562abfed88e03fd45b447", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e983c7fa4bfabecc448c813cef02922c100", "name": "google_sign_in_ios-google_sign_in_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983b0eb6bf04eb9be2ba23f1911e7be724", "name": "google_sign_in_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
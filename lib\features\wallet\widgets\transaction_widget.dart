import 'package:flutter/material.dart';
import 'package:hexacom_user/common/widgets/custom_asset_image_widget.dart';
import 'package:hexacom_user/features/wallet/domain/models/wallet_transaction_model.dart';
import 'package:hexacom_user/helper/date_converter_helper.dart';
import 'package:hexacom_user/helper/price_converter_helper.dart';
import 'package:hexacom_user/localization/language_constrants.dart';
import 'package:hexacom_user/provider/localization_provider.dart';
import 'package:hexacom_user/utill/dimensions.dart';
import 'package:hexacom_user/utill/images.dart';
import 'package:hexacom_user/utill/styles.dart';
import 'package:provider/provider.dart';


class TransactionWidget extends StatelessWidget {
  final Transaction? transaction;
  final bool isLastIndex;
  const TransactionWidget({super.key, this.transaction, required this.isLastIndex});

  @override
  Widget build(BuildContext context) {
    final bool isLtr = Provider.of<LocalizationProvider>(context, listen: false).isLtr;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: Dimensions.paddingSizeSmall),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [

        Row(children: [

          Expanded(child: Column(crossAxisAlignment: CrossAxisAlignment.start,children: [

            Row(children: [
              CustomAssetImageWidget(transaction?.direction == 'credit'
                  ? Images.creditIcon : Images.debitIcon,
                height: Dimensions.paddingSizeDefault,
                width: Dimensions.paddingSizeDefault,
              ),
              const SizedBox(width: Dimensions.paddingSizeExtraSmall),

              if(isLtr) Text( transaction?.direction == 'credit' ? '+ ' : '- ',
                style: rubikSemiBold.copyWith(color: Theme.of(context).textTheme.bodyLarge?.color,
                    fontSize: Dimensions.fontSizeDefault),
              ),

              Text(PriceConverterHelper.convertPrice(transaction?.amount),
                style: rubikSemiBold.copyWith(color: Theme.of(context).textTheme.bodyLarge?.color,
                    fontSize: Dimensions.fontSizeDefault),
              ),

              if(!isLtr) Text( transaction?.direction == 'credit' ? ' +' : ' -',
                style: rubikSemiBold.copyWith(color: Theme.of(context).textTheme.bodyLarge?.color,
                    fontSize: Dimensions.fontSizeDefault),
              ),
            ]),
            const SizedBox(height: Dimensions.paddingSizeExtraSmall,),

            if(transaction?.type != null)
            Text(
              getTranslated('${transaction?.type}', context),
              style: rubikRegular.copyWith(color: Theme.of(context).textTheme.bodyLarge?.color?.withValues(alpha: 0.70), fontSize: Dimensions.fontSizeSmall),
            ) ,

          ])),

          Column(crossAxisAlignment: CrossAxisAlignment.end,children: [

            Text(DateConverterHelper.estimatedDateYear(DateTime.parse(transaction!.createdAt!)), style: rubikRegular.copyWith(
              color: Theme.of(context).textTheme.bodyLarge?.color?.withValues(alpha: 0.50),
              fontSize: Dimensions.fontSizeSmall,
            )),
            const SizedBox(height: Dimensions.paddingSizeExtraSmall,),


            Text(transaction?.direction == 'credit' ? 'Credit': 'Debit', style: rubikRegular.copyWith(
              color: transaction?.direction == 'credit' ? Colors.green: Colors.red,
              fontSize: Dimensions.fontSizeSmall,
            )),
          ]),
        ]),

        if(!isLastIndex)
          Padding(
            padding: const EdgeInsets.only(top: Dimensions.paddingSizeSmall),
            child: Divider(thickness: .4,color: Theme.of(context).hintColor.withValues(alpha:.8)),
          ),
      ]),
    );
  }
}

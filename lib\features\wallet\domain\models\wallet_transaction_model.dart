
import 'package:hexacom_user/helper/date_converter_helper.dart';

class WalletTransactionModel {
  String? responseCode;
  String? message;
  int? totalSize;
  int? limit;
  int? offset;
  List<Transaction>? transaction;
  List<String>? errors;
  Filters? filters;

  WalletTransactionModel(
      {this.responseCode,
        this.message,
        this.totalSize,
        this.limit,
        this.offset,
        this.transaction,
        this.errors,
        this.filters
      });

  WalletTransactionModel.fromJson(Map<String, dynamic> json) {
    responseCode = json['response_code'];
    message = json['message'];
    totalSize = int.tryParse(json['total_size'].toString());
    limit = int.tryParse(json['limit'].toString());
    offset = int.tryParse(json['offset'].toString());
    if (json['data'] != null) {
      transaction = <Transaction>[];
      json['data'].forEach((v) {
        transaction!.add(Transaction.fromJson(v));
      });
    }
    errors = json['errors'].cast<String>();
    filters =
    json['filters'] != null ? Filters.fromJson(json['filters']) : null;
  }
}

class Transaction {
  int? id;
  String? type;
  String? direction;
  double? amount;
  String? reference;
  String? description;
  String? createdAt;

  Transaction(
      {this.id,
        this.type,
        this.direction,
        this.amount,
        this.reference,
        this.description,
        this.createdAt});

  Transaction.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    type = json['type'];
    direction = json['direction'];
    amount = double.tryParse(json['amount'].toString()) ?? 0;
    reference = json['reference'];
    description = json['description'];
    createdAt = json['created_at'];
  }
}

class Filters {
  String? filterBy;
  DateTime? startDate;
  DateTime? endDate;

  Filters({this.filterBy, this.startDate, this.endDate});

  Filters.fromJson(Map<String, dynamic> json) {
    filterBy = json['filter_by'];
    startDate = DateConverterHelper.convertDurationDateTimeFromString(json['start_date']);
    endDate = DateConverterHelper.convertDurationDateTimeFromString(json['end_date']);
  }
}

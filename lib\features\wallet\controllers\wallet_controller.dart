import 'package:flutter/material.dart';
import 'package:hexacom_user/common/models/api_response_model.dart';
import 'package:hexacom_user/features/wallet/domain/models/wallet_transaction_model.dart';
import 'package:hexacom_user/features/wallet/domain/repositories/wallet_repository.dart';
import 'package:hexacom_user/features/wallet/widgets/wallet_filter_bottom_sheet_widget.dart';
import 'package:hexacom_user/helper/api_checker_helper.dart';
import 'package:hexacom_user/utill/dimensions.dart';

class WalletProvider extends ChangeNotifier {
  final WalletRepository walletRepository;
  WalletProvider({required this.walletRepository});

  bool _isLoading = false;
  bool _firstLoading = false;
  bool get isLoading => _isLoading;
  bool get firstLoading => _firstLoading;
  WalletTransactionModel? _walletTransactionModel;
  WalletTransactionModel? get walletTransactionModel => _walletTransactionModel;
  OverlayEntry? _overlayEntry;
  bool _isOverlayShowing = false;

  DateTime? _startDate;
  DateTime? get startDate => _startDate;

  DateTime? _endDate;
  DateTime? get endDate => _endDate;

  String? _selectedFilterBy;
  String? get selectedFilterBy => _selectedFilterBy;

  bool get isOverlayShowing => _isOverlayShowing;


  static const List<String> filterTypeList = ['all', 'debit', 'credit'];



  Future<void> getTransactionList(int offset, {
    bool reload = false,
    bool isUpdate = true,
    String? filterBy,
    DateTime? startDate,
    DateTime? endDate,

  }) async {
    if(reload || offset == 1) {
      _walletTransactionModel = null;

      if(isUpdate) {
        notifyListeners();
      }
    }

    ApiResponseModel apiResponse = await walletRepository.getList(
      offset : offset, filterBy: filterBy, startDate: startDate,
      endDate: endDate,
    );

    if (apiResponse.response?.data != null && apiResponse.response?.statusCode == 200) {
      if(offset == 1) {
        _walletTransactionModel = WalletTransactionModel.fromJson(apiResponse.response?.data);
      }else {
        _walletTransactionModel?.offset  = WalletTransactionModel.fromJson(apiResponse.response?.data).offset;
        _walletTransactionModel?.totalSize  = WalletTransactionModel.fromJson(apiResponse.response?.data).totalSize;
        _walletTransactionModel?.transaction?.addAll(WalletTransactionModel.fromJson(apiResponse.response?.data).transaction ?? []);

      }

    } else {
      _walletTransactionModel?.transaction = [];
      ApiCheckerHelper.checkApi(apiResponse);
    }
    notifyListeners();
  }


  void showBottomLoader() {
    _isLoading = true;
    notifyListeners();
  }

  void removeFirstLoading() {
    _firstLoading = true;
    notifyListeners();
  }



  int currentIndex = 0;
  void setCurrentIndex(int index) {
    currentIndex = index;
    notifyListeners();
  }


  Future<void> setSelectedDate({required DateTime? startDate, required DateTime? endDate}) async {
    _startDate = startDate;
    _endDate = endDate;
    notifyListeners();
  }


  void setSelectedProductType({String? type, bool isUpdate = true}){
    _selectedFilterBy = type;

    if(isUpdate){
      notifyListeners();
    }
  }


  void initFilterData(bool fromDateRangePicker){

    if(!fromDateRangePicker){
      _selectedFilterBy = _walletTransactionModel?.filters?.filterBy;
      _startDate = _walletTransactionModel?.filters?.startDate;
      _endDate = _walletTransactionModel?.filters?.endDate;
    }

  }


  void showOverlay(Size size, BuildContext context, {bool fromDateRangePicker = false}){
    _isOverlayShowing = true;
    double width = (size.width - Dimensions.webScreenWidth) / 2;

    _overlayEntry = OverlayEntry(builder: (context) => Positioned(
        right: width + Dimensions.paddingSizeSmall,
        top: 160,
        width: 360,
        child: WalletFilterBottomSheetWidget(fromDateRangePicker: fromDateRangePicker)),
    );

    final overlay = Overlay.of(context);
    overlay.insert(_overlayEntry!);
  }

  void hideOverlay(){
    _isOverlayShowing = false;
    _overlayEntry?.remove();
  }


}

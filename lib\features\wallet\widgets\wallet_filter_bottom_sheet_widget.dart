import 'package:flutter/material.dart';
import 'package:hexacom_user/common/widgets/custom_asset_image_widget.dart';
import 'package:hexacom_user/common/widgets/custom_button_widget.dart';
import 'package:hexacom_user/common/widgets/custom_calendar_widget.dart';
import 'package:hexacom_user/common/widgets/custom_date_range_picker_widget.dart';
import 'package:hexacom_user/common/widgets/custom_single_child_list_widget.dart';
import 'package:hexacom_user/features/wallet/controllers/wallet_controller.dart';
import 'package:hexacom_user/features/wallet/domain/models/wallet_transaction_model.dart';
import 'package:hexacom_user/helper/date_converter_helper.dart';
import 'package:hexacom_user/helper/responsive_helper.dart';
import 'package:hexacom_user/localization/language_constrants.dart';
import 'package:hexacom_user/utill/dimensions.dart';
import 'package:hexacom_user/utill/images.dart';
import 'package:hexacom_user/utill/styles.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class WalletFilterBottomSheetWidget extends StatefulWidget {
  final bool fromDateRangePicker;
  const WalletFilterBottomSheetWidget({super.key, this.fromDateRangePicker = false});

  @override
  State<WalletFilterBottomSheetWidget> createState() => _WalletFilterBottomSheetWidgetState();
}

class _WalletFilterBottomSheetWidgetState extends State<WalletFilterBottomSheetWidget> {

  static const List<String> filterTypeList = ['all', 'debit', 'credit'];


  @override
  void initState() {
    super.initState();

    Provider.of<WalletProvider>(context, listen: false).initFilterData(widget.fromDateRangePicker);
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.sizeOf(context);

    return Consumer<WalletProvider>(builder: (context, walletProvider, _) {
      return Container(
        constraints: BoxConstraints(maxHeight: ResponsiveHelper.isDesktop(context) ? size.height * 0.48 : size.height * 0.55),
        decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(Dimensions.paddingSizeSmall),
              topRight: Radius.circular(Dimensions.paddingSizeSmall),
            ),
            boxShadow: [BoxShadow(
              color: Theme.of(context).primaryColor.withValues(alpha: .05),
              blurRadius: 17.89,
              offset: const Offset(0, 4.77),
            )]
        ),
        child: Column(children: [
          _FilterTitleWidget(walletProvider: walletProvider),

          Divider(height: 1, color: Theme.of(context).hintColor.withValues(alpha: .15), thickness: 1),

          Expanded(child: SingleChildScrollView(child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [

              ///Filter by
              Padding(
                padding: const EdgeInsets.symmetric(
                    vertical: Dimensions.paddingSizeLarge,
                    horizontal: Dimensions.paddingSizeLarge
                ),
                child: Text(getTranslated('filter_by', context),
                  style: rubikMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                ),
              ),

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge),
                child: CustomSingleChildListWidget(
                  scrollDirection: Axis.horizontal,
                  itemCount: filterTypeList.length,
                  itemBuilder: (index) {
                    return InkWell(
                      onTap: () => walletProvider.setSelectedProductType(type: filterTypeList[index]),
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                          horizontal: Dimensions.fontSizeThirty,
                          vertical: Dimensions.paddingSizeSmall,
                        ),
                        margin: EdgeInsets.only(right: Dimensions.paddingSizeLarge),
                        constraints: const BoxConstraints(minWidth: 50),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: filterTypeList[index] == walletProvider.selectedFilterBy ?
                            Theme.of(context).primaryColor :
                            Theme.of(context).hintColor.withValues(alpha: 0.5),
                          ),
                          borderRadius: BorderRadius.circular(Dimensions.radiusSizeFifty),
                        ),
                        child: Text(
                          getTranslated(filterTypeList[index], context),
                          style: rubikRegular.copyWith(
                            color: walletProvider.selectedFilterBy == filterTypeList[index] ?
                            Theme.of(context).primaryColor :
                            Theme.of(context).hintColor,
                            fontSize: Dimensions.fontSizeDefault,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: Dimensions.paddingSizeExtraLarge),

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge),
                child: Divider(height: 1, color: Theme.of(context).hintColor.withValues(alpha: .15), thickness: 1),
              ),
              const SizedBox(height: Dimensions.paddingSizeExtraLarge),


              ///Date Range
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge),
                child: Text(
                  getTranslated('date_range', context),
                  style: rubikMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                ),
              ),
              const SizedBox(height: Dimensions.paddingSizeSmall),

              InkWell(
                onTap: () {
                  if(walletProvider.isOverlayShowing){
                    walletProvider.hideOverlay();
                  }

                  showDialog(context: context, builder: (BuildContext context) {
                    return Dialog(child: SizedBox(height: 400, child: CustomCalendarWidget(
                      initDateRange: PickerDateRange(walletProvider.startDate, walletProvider.endDate),
                      onSubmit: (range) {
                        walletProvider.setSelectedDate(
                          startDate: range?.startDate,
                          endDate: range?.endDate,
                        );

                        if(ResponsiveHelper.isWeb()){
                          walletProvider.showOverlay(size, context, fromDateRangePicker: true);
                        }
                      },
                      onCancel: (){
                        Navigator.of(context).pop();

                        if(ResponsiveHelper.isWeb()){
                          walletProvider.showOverlay(size, context, fromDateRangePicker: true);
                        }
                      },
                    )));
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: Dimensions.paddingSizeExtraSmall),
                    decoration: BoxDecoration(
                        border: Border.all(width: 1, color: Theme.of(context).hintColor.withValues(alpha: .15)),
                        borderRadius: BorderRadius.circular(Dimensions.paddingSizeExtraSmall)
                    ),
                    child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                        CustomDateRangePickerWidget(
                          text: walletProvider.startDate == null ?
                          'dd-mm-yyyy' :
                          DateConverterHelper.dateStringMonthYear(walletProvider.startDate),
                        ),

                        const Icon(Icons.horizontal_rule, size: Dimensions.paddingSizeLarge),

                        CustomDateRangePickerWidget(
                          text: walletProvider.endDate == null ?
                          'dd-mm-yyyy' :
                          DateConverterHelper.dateStringMonthYear(walletProvider.endDate),
                        ),
                      ]),

                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeSmall),
                        child: CustomAssetImageWidget(
                          Images.calendarIcon,
                          height: Dimensions.paddingSizeDefault,
                          width: Dimensions.paddingSizeDefault,
                        ),
                      ),
                    ]),
                  ),
                ),
              ),
              const SizedBox(height: Dimensions.paddingSizeExtraLarge),
            ],
          ))),

          ///Bottom button
          SafeArea(child: Container(
            padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeDefault),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 10,
              )],
            ),
            height: 75,
            child: Row(children: [
              Expanded(child: CustomButtonWidget(
                btnTxt: getTranslated('clear_filter', context),
                backgroundColor: Theme.of(context).hintColor.withValues(alpha: .125),
                height: 45,
                style: rubikMedium.copyWith(color: Theme.of(context).textTheme.bodyLarge?.color),
                onTap: () {
                  if(walletProvider.isOverlayShowing){
                    walletProvider.hideOverlay();
                  }else{
                    Navigator.pop(context);
                  }

                  if(_isClearFilterData(walletProvider.walletTransactionModel)) {
                    walletProvider.getTransactionList(1);
                  }

                },
              )),
              const SizedBox(width: Dimensions.paddingSizeLarge),

              Expanded(child: CustomButtonWidget(
                 isLoading: walletProvider.walletTransactionModel == null,
                btnTxt: getTranslated('filter', context),
                backgroundColor: _canFilter() ? Theme.of(context).primaryColor : Theme.of(context).disabledColor,
                height: 45,
                onTap:_canFilter() ? (){

                  walletProvider.getTransactionList(
                      1,
                      endDate: walletProvider.endDate,
                      startDate: walletProvider.startDate,
                      filterBy: walletProvider.selectedFilterBy
                  );

                  if(walletProvider.isOverlayShowing){
                  walletProvider.hideOverlay();
                  }else{
                    Navigator.pop(context);
                  }

                } : null,
              )),

            ]),
          )),
        ]),
      );
    });
  }

  bool _canFilter() {
    final WalletProvider walletProvider = Provider.of<WalletProvider>(context, listen: false);

    if (walletProvider.walletTransactionModel == null) return true;

    return walletProvider.endDate != walletProvider.walletTransactionModel?.filters?.endDate ||
        walletProvider.startDate != walletProvider.walletTransactionModel?.filters?.startDate ||
        walletProvider.selectedFilterBy != walletProvider.walletTransactionModel?.filters?.filterBy;
  }


  bool _isClearFilterData(WalletTransactionModel? walletModel) {
    return walletModel?.filters?.startDate != null ||
        walletModel?.filters?.endDate != null ||
        (walletModel?.filters?.filterBy != null && (walletModel?.filters?.filterBy?.isNotEmpty ?? false));
  }

}



class _FilterTitleWidget extends StatelessWidget {
  final WalletProvider walletProvider;
  const _FilterTitleWidget({required this.walletProvider});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeLarge, vertical: Dimensions.paddingSizeDefault),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
        Text(getTranslated('filter_data', context), style: rubikMedium.copyWith(fontSize: Dimensions.fontSizeLarge)),

        InkWell(
          onTap: (){
            if(walletProvider.isOverlayShowing){
             walletProvider.hideOverlay();
            }else{
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            }

          },
          child: Container(
            padding: const EdgeInsets.all(Dimensions.paddingSizeSmall),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              color: Theme.of(context).hintColor.withValues(alpha: .125),
            ),
            child: Center(child: Icon(Icons.clear, size: Dimensions.paddingSizeDefault)),
          ),
        )
      ]),
    );
  }
}


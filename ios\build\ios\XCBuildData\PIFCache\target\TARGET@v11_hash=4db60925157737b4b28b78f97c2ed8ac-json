{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d40b67a09ac7d1d16355bddb377c8f5b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98124d05d95b0a88b50ba23971f882cc63", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b888a93c8622cad45a9b4f430c1b5d77", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e60d334a0636be678d24ed155f17e68", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b888a93c8622cad45a9b4f430c1b5d77", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850968fc64e545db8702a9275a14bb5b9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bc87581cba9e8a724bef9748094d63b3", "guid": "bfdfe7dc352907fc980b868725387e985e35902722fa51253ddf7f2db938da97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d98efdf86d0e2115e791fbc11f7f2ab", "guid": "bfdfe7dc352907fc980b868725387e988cb2966c93cbeaa485df350548addf94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b01bfedce262927a9755229f9dc73e", "guid": "bfdfe7dc352907fc980b868725387e98bc6255dd2ede8637ea436c655857d417", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804ae9b66374293942a7692b097ad89e5", "guid": "bfdfe7dc352907fc980b868725387e982ec39c66c4aa7512a695ebe85813937e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98986152a5cc66ed2d3c665ac838493952", "guid": "bfdfe7dc352907fc980b868725387e98faedbd6fc02bd573fc4230ab1435ae91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f902bdb0114f32e66a5acd6e4276dfd5", "guid": "bfdfe7dc352907fc980b868725387e98a8d3bc5565660649c9f8f2cb8bc30948", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b106c00871e6e0c5bca2b18585d782", "guid": "bfdfe7dc352907fc980b868725387e9896cfcc0a7c556c19046865d9dc5e4e54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886a0d99dce64ffa932b8f3e97ccf8946", "guid": "bfdfe7dc352907fc980b868725387e98fac09f64dcbf1d8597740f72046713e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986589d2a6902c00671aef9c1855770b26", "guid": "bfdfe7dc352907fc980b868725387e98846d3e90e649ad89c98bbda7c034e256", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf3052b7f16723eb1b95f642d94ba6cb", "guid": "bfdfe7dc352907fc980b868725387e9857bcd38a911a3e8edddb8accb04857b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f13969ec38989c8321c186077a3d8e0b", "guid": "bfdfe7dc352907fc980b868725387e98a09ad8356b296de5f88705b3f80bfd2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2bf3778b79e7c6f275121e38c7d1c3a", "guid": "bfdfe7dc352907fc980b868725387e98bb0f3c7b1aeac7c33c4d947e2132e6b6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9851198fae0d8905fb79f391da70d27b9a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be0f57f7cf897ad6f3fcc964e0a052a0", "guid": "bfdfe7dc352907fc980b868725387e9806ca13c36fada04737fab64394e4a672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983349b5abb5dd26372809cd60b2c657b0", "guid": "bfdfe7dc352907fc980b868725387e984b9b1fc755ebd228d95922f0b77fdc0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a6b0477580264f641f599a60152dfcd", "guid": "bfdfe7dc352907fc980b868725387e98b5274c5245c758169010756d95f11cf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c0115d9e335dd4d36c5e7a11584ecf3", "guid": "bfdfe7dc352907fc980b868725387e986155eb0b18e160090f1c3de162b97ac8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c193844147ad51404dcba812068cf88a", "guid": "bfdfe7dc352907fc980b868725387e98d1cd60dab61b8f6a5d56eb5416544dff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca4ca2546fa50d724dd5ac838f2d1b02", "guid": "bfdfe7dc352907fc980b868725387e98dc14ef74b9ab13b7d86e26925f1dda77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98448a6aa30673ca65f8c688b5df252e36", "guid": "bfdfe7dc352907fc980b868725387e98e2c851e8f0e990d544a2e9ef5aeaaa13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d0852ff8156d5c8c4f6fee24a0b1fb5", "guid": "bfdfe7dc352907fc980b868725387e98e9375e23c6c47ac8f5ae87fd56b17b52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e92ebbece6220dbb1f19d227a4b11965", "guid": "bfdfe7dc352907fc980b868725387e98e8cb6ce462391dd39bfa30e1070fa3b6"}], "guid": "bfdfe7dc352907fc980b868725387e98c53cda76c6714cc99d33f8d438858f07", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98cdd7430e01d92244c5d5c3d4bd77581d"}], "guid": "bfdfe7dc352907fc980b868725387e98e25737a103f495bf6b667e8f6f32d4bd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9804c93d896fd7e7654fa5c52570948a90", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e9868af60886bbf96f1bb9951e1a108aa20", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
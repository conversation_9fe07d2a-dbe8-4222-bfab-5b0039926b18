{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98955e2840aab25b846a40dbf24c73bd3e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9899a2a8820f9f4545faba130bba100190", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb7c2cc01065790d87650330c9594e2a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f68b1c8e9713bd883fa7effbcd883c0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb7c2cc01065790d87650330c9594e2a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98172caa0c7e86abd796c6109d91632681", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9894a2d4ab8b6526bdaa87033e2ca9175c", "guid": "bfdfe7dc352907fc980b868725387e985ee2a367bdb1c5b295db5e823bfbce34", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9892dbfbf2e4a3557ea3bb5d5b8037e796", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f4b951de0f1c866d1507d4c032efa72", "guid": "bfdfe7dc352907fc980b868725387e98585eb2064e6e118d224f8ee485083789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea989faf322f84237d175f3ade227f17", "guid": "bfdfe7dc352907fc980b868725387e981a10cc837d23e4084efc3d2a40f4312a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e587e6a405e98a3a18fe262bf5d3fe85", "guid": "bfdfe7dc352907fc980b868725387e980b55a2c09917549ea9e3a0f611666751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8fcd47ff8cb81f9db5aef941f514cf6", "guid": "bfdfe7dc352907fc980b868725387e984b7198769465f600e29464c8ca978560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe0bdc5535d041dfe080e8e017b4ae8a", "guid": "bfdfe7dc352907fc980b868725387e985619fc3153e2b508e1332607b5f4b889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98165f2248fb6fe0b84c4037cad0cac958", "guid": "bfdfe7dc352907fc980b868725387e98f06e17de926c181258c28b6e76d3ad08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a0e3f0fc450d83cbf94bd847731c08", "guid": "bfdfe7dc352907fc980b868725387e98810813adf642fda0e5c73edbc8ede36e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1d1d5907b8ab2c60597c438d1fc7eb2", "guid": "bfdfe7dc352907fc980b868725387e98a6910260f09365fa21fa584da6b2f4f5"}], "guid": "bfdfe7dc352907fc980b868725387e988343f40fcd7f4d8e8798b73c10af6714", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98465734d9dddd3b94519683fad57f2ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e985b40cd1f827b7f9a157f054c72acf05c"}], "guid": "bfdfe7dc352907fc980b868725387e986075d94b693d9af108675649b328a684", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98548f3848f15e7ea52c6ed25ee60c8c8e", "targetReference": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3"}], "guid": "bfdfe7dc352907fc980b868725387e986eb35e750b6206866ba7365315695732", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3", "name": "GTMAppAuth-GTMAppAuth_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98855fb84830a2ff40ce73a17fc283f650", "name": "GTMAppAuth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
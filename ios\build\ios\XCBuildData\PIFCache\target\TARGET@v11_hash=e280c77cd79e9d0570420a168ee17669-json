{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd0c41937d251c75fd4e222abdec8442", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1cc874a020a479547cea61eca3df2b6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e83a9022250efc1dd385dc4dd908d5cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d47dd7ba22ca9f0b4fa96fadfb06490e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e83a9022250efc1dd385dc4dd908d5cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e6188ca4110dd4031a8a69eedacc01eb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b41e39c1a162fbcd720b15947783fb", "guid": "bfdfe7dc352907fc980b868725387e9857f3f3bd87180e1fccc9d5b1d0ecc539", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c2318b7ff1d1c10a1a325f0ef0e2ac", "guid": "bfdfe7dc352907fc980b868725387e98f2ec65b19effa60e849cfb63135f5d19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a16938462f34191ebebb403cb8985ab4", "guid": "bfdfe7dc352907fc980b868725387e9890f20e55806666a484343d9cecf2cf7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae215750c0874c8c365db967f43cf99", "guid": "bfdfe7dc352907fc980b868725387e984dfd1caa427974dcca1de82ce1639d9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0ccf12a49060f664105d19b91b22c84", "guid": "bfdfe7dc352907fc980b868725387e98c176b0543fe64a6a98463fccd3d03dfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830d7ae51d4c2d51317ae405b9b72dfb9", "guid": "bfdfe7dc352907fc980b868725387e98b6acbeb0c5313cc8ce28ba95371b9133", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1213ebfddb6a4707051d4a9e81a09a", "guid": "bfdfe7dc352907fc980b868725387e98050805f4be9a502d611240d2fa0cecd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af5047d28e343b0e8813269eb2c1deb8", "guid": "bfdfe7dc352907fc980b868725387e982285b32ebb373c605896886ef6bd5e12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc8744e586f3d55ea151b0c744379e0", "guid": "bfdfe7dc352907fc980b868725387e980df22dc5519eecc8e2e29ff85b4e0403", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98327310d6e168dd1e34e911426ff270e5", "guid": "bfdfe7dc352907fc980b868725387e98c6add1f4b74d9dedea30350ba025926a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa8ec74603458127a6d7bb6bdb66d504", "guid": "bfdfe7dc352907fc980b868725387e98f97d78c653f7209fcc5763f86c31607b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868056630f1f4f644523363ddbb3f6758", "guid": "bfdfe7dc352907fc980b868725387e98e4935c01328af474ee30044d22068e66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c5aa5e4e23e78fd8bb677c7ec7f159", "guid": "bfdfe7dc352907fc980b868725387e98b5eab8227c90347b2b378fb7785451af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981efa58af9c9ba99be1f23c09504b9263", "guid": "bfdfe7dc352907fc980b868725387e98d573aea95296f04f88aa326dcf8a93ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986812e8fbcaafd29bd4211f8738d99bcf", "guid": "bfdfe7dc352907fc980b868725387e98d0cc481a7aaa506e37521d62664e279a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62215977e77faf620f8456322f63af9", "guid": "bfdfe7dc352907fc980b868725387e98229521aa2b4b32d588db97644429c411", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982a1d921d86947a34d02991e7a955939a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98737bf7a520aa035e0e371e6fd5c76030", "guid": "bfdfe7dc352907fc980b868725387e98abfacce0ea3d3dc7a80906e1cced2367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7443f3fee05c2d1e5c72c4f1ecd203c", "guid": "bfdfe7dc352907fc980b868725387e9810cfb9f6f764ce729df83910ebeaf551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981441e26649be91cf4234bf728aee8de9", "guid": "bfdfe7dc352907fc980b868725387e98a959dcf6b58680a91749fa268a26d661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c90bb601145dfa7e2bfc0b5fc4ac5d0", "guid": "bfdfe7dc352907fc980b868725387e98124ffd83f4fb2de8b3efe57610058d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d025edc36b3f9ca3afe2ccc7165f7e0", "guid": "bfdfe7dc352907fc980b868725387e98f7e2fdc5fa895ed58141c8c308102c62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a7938a222a5ef3ed43de3967e49f7d0", "guid": "bfdfe7dc352907fc980b868725387e98b4bc26af3fd19d0fa29f663a24c777b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeeb624c3ddc7513dc4504710070cb0f", "guid": "bfdfe7dc352907fc980b868725387e98c59068fa8503cad50b4ec66ba6fb7142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eff15aafbd43c8f38b371ac20ca0948", "guid": "bfdfe7dc352907fc980b868725387e981c4131d91b18383c2fe590d9d5db2728"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5da5ef48f9a3620f964ccc60e12520f", "guid": "bfdfe7dc352907fc980b868725387e98275d84b6993e46a75067b2e66e37cc23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842378d5578f78a5f350323116e520033", "guid": "bfdfe7dc352907fc980b868725387e98f9fffbdfa7a0cc1fa36450812b129354"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ab2350f8bb02436f60e5e90f16712e7", "guid": "bfdfe7dc352907fc980b868725387e98f6c13bb5326e9b1d19cffd4f8b70ae8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98588526ea5146878afe0108d69ea22c3e", "guid": "bfdfe7dc352907fc980b868725387e98749d66167f8a2c858175ff07441f2bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fcddd2d3b0e9aee248cb2f79d8dddd1", "guid": "bfdfe7dc352907fc980b868725387e98f60c4da1d30f5c651773fae03a95a1e9"}], "guid": "bfdfe7dc352907fc980b868725387e9888ddff36bfdb8fba1896fffbff290975", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98e21cb5e95267ac01a60b3f75a1af0e91"}], "guid": "bfdfe7dc352907fc980b868725387e980e1ce268bfb1c82d099ffdb3b17b4474", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e20954141ebd835c2535267f91ccb9ea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a143de2165105318ef45cdf55486c71b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c524bade818f988421a1a90455a568d8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ed3f2ab4fea5b088206cd73f3fbc968e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9846010f703907a6d1edbdc5c1d2edc308", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ed3f2ab4fea5b088206cd73f3fbc968e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0ad65bc8cc5d32e1cd05f26bc730c2b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989594bef04a9df3a888cd90df1d86da6c", "guid": "bfdfe7dc352907fc980b868725387e9839605ee7b4b9e814d6d94685105f769e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831fd95278f470dcea1eb3f99623a2e4d", "guid": "bfdfe7dc352907fc980b868725387e98b6e5ca3f52a167c451e0110413442a2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d83fef8309b8b732cdd855b7239566", "guid": "bfdfe7dc352907fc980b868725387e988fac3d187f1b89c49cb59f7053796678", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98acd4e8a85aa9bc3f7fbf04eefa4338d6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987d46b9f9f5acc760d33424c006fc9eaa", "guid": "bfdfe7dc352907fc980b868725387e9814f84c336cb53f105bbd7e995221ead4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15067750f1c3336cc8e40bdc496e947", "guid": "bfdfe7dc352907fc980b868725387e98e4fea9c6eb52d9ef7fa49e1da2cb58d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd82aba2de67fb50ca80a679cc85fc68", "guid": "bfdfe7dc352907fc980b868725387e987884b194d8a2361180c9577fac8b9c57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cac4521387fa7dd508daaa27a9cf7c3", "guid": "bfdfe7dc352907fc980b868725387e98ed9a515c97ec376895a9d729b4efa12d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3f21e2ae09fd4dea9b04a85083468cf", "guid": "bfdfe7dc352907fc980b868725387e98ec836df6c009afe020ec9cf1dfab109a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987845404e560d61d5a5d4d838ba43e8f4", "guid": "bfdfe7dc352907fc980b868725387e98364c990fb73a95e804f5ec598bce683d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98852e8fbaf79899f6ebfefd72795f994d", "guid": "bfdfe7dc352907fc980b868725387e98c4da2c95b9c93c445b1bcfa51467dd4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be5511ebbc6b44593a79e279144631c7", "guid": "bfdfe7dc352907fc980b868725387e98b23d4702402fca90d174b04fec0ffd78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984873d0074b58281da76703f21be49280", "guid": "bfdfe7dc352907fc980b868725387e98a00643d683e073dbfe4615ada98c2c54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dc8c45c0ca11f536fa71c5125ba907d", "guid": "bfdfe7dc352907fc980b868725387e9820702d8bfa1064dbed1ad40581b5a17f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8c0faf149a3e3b1ae9cb0bc57e94e6", "guid": "bfdfe7dc352907fc980b868725387e98db643b2d182f367c0198b217c5fb8ada"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7deddd9a9ace33e80bc880f6d7cecd", "guid": "bfdfe7dc352907fc980b868725387e98f99d6568adf0aa189f2b7049691b765c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff6448d04c50a10a778f6255c5371a99", "guid": "bfdfe7dc352907fc980b868725387e98991528b3020c45349a3496c463ab721a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841dbe4a3be4d52ecd10cb142fbc5c4a5", "guid": "bfdfe7dc352907fc980b868725387e98361cfc51bfc016f561c1b659275b4c5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98887936770ec62047a5d0b18a3ebd0fca", "guid": "bfdfe7dc352907fc980b868725387e981e6280ee58d462bd1493e285afb61f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fcbbec1154497fc970a06583633b4ec", "guid": "bfdfe7dc352907fc980b868725387e9801300c1295c60de85591f687f347f871"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f52164808cf7f8dcbb8f8190a5b79e7", "guid": "bfdfe7dc352907fc980b868725387e98a1841dd41ca4bdf0a75e94d5f4f87f56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985edfa1dfe2f567e0d761ee26d17ca709", "guid": "bfdfe7dc352907fc980b868725387e9847d39e90d4c883a5b14431a1f55c69a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2e645fbcca42a9c13a48fdb8ceab5f3", "guid": "bfdfe7dc352907fc980b868725387e98dce21d008f65df8511c2fbd0eaed7bed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b70ddd99805dcaa4cd0829adf858e176", "guid": "bfdfe7dc352907fc980b868725387e9801740694092aee68afff4a407064eb11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c457a4ad67ae64bc7108d7309f7e07d3", "guid": "bfdfe7dc352907fc980b868725387e9855d9f8677ca9810637a4efc7a986342b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822958f9737a30c101c26957e82461659", "guid": "bfdfe7dc352907fc980b868725387e984ff406dd3c73ff2865f69b6e64fe74f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cfce1473be04651645f7958f9d43f7a", "guid": "bfdfe7dc352907fc980b868725387e98a7a3a9aac2514f919c741f6aa3533c35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba9d1f5064826b7c5145a1d1d9465ca", "guid": "bfdfe7dc352907fc980b868725387e9845a9fe21154a9cf676ecee984782f2b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3a5620cdde3e402a0204f39a93c0962", "guid": "bfdfe7dc352907fc980b868725387e98194ebacad3dcbf65d04899515d9180c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e65db4f90675138a792a02f7e2b816f2", "guid": "bfdfe7dc352907fc980b868725387e984e0195ad8cc8ecbc556336ed2e6b88c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983363ec6ebeef2a2a98645cea65a3be84", "guid": "bfdfe7dc352907fc980b868725387e986ac47db01f901ccd5aed05f6fc89ed1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd2cade2fd158454ddb9d7a51ccc99d2", "guid": "bfdfe7dc352907fc980b868725387e98e0f51a0cf0a78b403fe74895ecab9f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e393f68b5f753a2f5c80488ec767033", "guid": "bfdfe7dc352907fc980b868725387e9897bdd4b686219e3e781c9800417ce58f"}], "guid": "bfdfe7dc352907fc980b868725387e9801be52bbd0a1a24e241597b61a76b556", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98716e8da6cad39eee057e81100e285981"}], "guid": "bfdfe7dc352907fc980b868725387e980be8f63b56f4fd12c1d15276fcb52e9e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b8e8b055c6a348a07d4c2d94dc095f76", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
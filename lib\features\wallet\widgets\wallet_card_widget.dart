import 'package:flutter/material.dart';
import 'package:hexacom_user/features/profile/providers/profile_provider.dart';
import 'package:hexacom_user/features/wallet/controllers/wallet_controller.dart';
import 'package:hexacom_user/helper/price_converter_helper.dart';
import 'package:hexacom_user/helper/responsive_helper.dart';
import 'package:hexacom_user/localization/language_constrants.dart';
import 'package:hexacom_user/utill/dimensions.dart';
import 'package:hexacom_user/utill/styles.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:provider/provider.dart';

class WalletCardWidget extends StatelessWidget {
  const WalletCardWidget({
    super.key, required this.tooltipController,
    required this.focusNode,
    required this.inputAmountController
  });

  final JustTheController tooltipController;
  final FocusNode focusNode;
  final TextEditingController inputAmountController;

  @override
  Widget build(BuildContext context) {
    final double widthSize = MediaQuery.sizeOf(context).width;
    return Consumer<WalletProvider>(builder: (context, walletProvider, _) {
      return Row(children: [
          Column(crossAxisAlignment: CrossAxisAlignment.start, mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center, children: [

              Text(getTranslated('wallet_amount', context), style: rubikRegular.copyWith(
                  color: Theme.of(context).cardColor, fontSize: Dimensions.fontSizeLarge,
              )),
              const SizedBox(height: Dimensions.paddingSizeSmall),

              Row(children: [
                Text(
                  PriceConverterHelper.convertPrice(Provider.of<ProfileProvider>(context).userInfoModel?.walletBalance ?? 0),
                  style: rubikBold.copyWith(color:  Theme.of(context).cardColor, fontSize: Dimensions.fontSizeOverLarge),
                ),
                const SizedBox(width: Dimensions.paddingSizeExtraSmall),

                  JustTheTooltip(
                    backgroundColor: Colors.black87,
                    controller: tooltipController,
                    preferredDirection: AxisDirection.down,
                    tailLength: 10,
                    tailBaseWidth: 20,
                    content: Container(
                      width: ResponsiveHelper.isDesktop(context) ? widthSize * 0.25 : widthSize * 0.57,
                      padding: const EdgeInsets.all(Dimensions.paddingSizeSmall),
                      child: Text(
                        getTranslated('this_wallet_balance_is_your', context),
                        style: rubikRegular.copyWith(color: Colors.white, fontSize: Dimensions.fontSizeDefault),
                      ),
                    ),
                    child: InkWell(
                      onTap: () => tooltipController.showTooltip(),
                      child: const Icon(Icons.info_outline, color: Colors.white),
                    ),
                  ),
                ],
              ),
            ],
          ),
      ]);
    });
  }
}

{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98272766e1dea883531f7ae2ccfe5f4259", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9822eb9e695b532ef3bb86015308055b67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d51cee018ec936c89d5aa817ae10cc66", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dbe6a89337c131d7b02339886df6af1b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d51cee018ec936c89d5aa817ae10cc66", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9897ffd9ea2a86f8c5e867a01c3e06d81e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d114f5bb15e1924e90e7bd543334dd12", "guid": "bfdfe7dc352907fc980b868725387e9821ec10d8a54fbade399d034048ea24c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2654e6361f4b4939e81db7939a5ef6", "guid": "bfdfe7dc352907fc980b868725387e981febd86f53d838c9ca7503114f0bc037", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989afe94546b643c7f0ed50619878d4692", "guid": "bfdfe7dc352907fc980b868725387e98e5550c334ad6598ea69e8fc5123bcaf7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8208e798f6ffd192e2225af861303a", "guid": "bfdfe7dc352907fc980b868725387e98002d679da3c48f008f687dd0a800d15a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892e86262ee38bee1a54b3f02a29a4876", "guid": "bfdfe7dc352907fc980b868725387e9828cb8c83618cfbefd02c73670c350399", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e38522828a614546ef57430db8a6d1", "guid": "bfdfe7dc352907fc980b868725387e98b6695950c9d9ee2b3ac0477b1b59c0d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d37a62653f18bfcc0ad5c4a3f9e20ae", "guid": "bfdfe7dc352907fc980b868725387e98c75d98c5ea11b94c9de7543947ab6ea5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51c34325bef45b0743ec5c8307dd108", "guid": "bfdfe7dc352907fc980b868725387e9862c96d9bddccf21bed11e8c6c6f1d807"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870a2e4b32ce09a5bd446e6e740c0b0be", "guid": "bfdfe7dc352907fc980b868725387e98f82879d77c8f462d7a92e2df70f3574b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98eb3d835662988496c95f471fdbfa8812", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8467916e157c8c8bd89f2d0b5423a64", "guid": "bfdfe7dc352907fc980b868725387e9898b599ceb0272f7641a651d06f693298"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed09c653d3951b37ba878b0c31b6f0e", "guid": "bfdfe7dc352907fc980b868725387e9809b182b23d621115ec6e42c5df78838a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f125b97f13f995ba406d5b75d811e328", "guid": "bfdfe7dc352907fc980b868725387e98a42238fb0cd68da489b20477e0d20ed6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff344da9cd3ef02e813c53edd78bcfae", "guid": "bfdfe7dc352907fc980b868725387e98c86ba3ccda776d62692d4c9e68098c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be80d3524d2f07b31a80d3460797dba3", "guid": "bfdfe7dc352907fc980b868725387e98cfa57d89435b4a2068b9f6be49cff9fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f26c967f941a37ac76f2d5aaba137d", "guid": "bfdfe7dc352907fc980b868725387e98de7de2f9a511f6aece5f22c7cba9c2bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ba772bfcbd524c5dd5205af33227cf9", "guid": "bfdfe7dc352907fc980b868725387e986a4eaeea629674973c9c7c983f85d0c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897d2acb3d8e2ba9f938910e0e728fdfc", "guid": "bfdfe7dc352907fc980b868725387e9808a389587c4b842dc3848da4ead77be8"}], "guid": "bfdfe7dc352907fc980b868725387e987674f0bd0f615c79a227e96b2ffdfb20", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e988fdf657b3618d75909ee0f22744b1bb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e98132a2a461902b662b449a186205826c8"}], "guid": "bfdfe7dc352907fc980b868725387e98d28c52d7de44d0895b7f3604045a71b3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987a755354d2198997531046a3c707ab9e", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98d8fe882dbc9cd384779cb0e8c7709eab", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e9865db3f167ba0efddab6d454999004e2f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fbf9bdd4f71a5bb3042a52b2d0d46112", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980253c70ba212a24a8b6f16f56b48f99e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98923c1b71ed1573983921f545457652f4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e5bb424f0b3389754c4d2c3bfab1b0f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98923c1b71ed1573983921f545457652f4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981345aeeece1d27a734313de821fe2e8c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6511fdae04133f03e0d4b81224546bc", "guid": "bfdfe7dc352907fc980b868725387e988018253e27939e578d21d96dd47cde5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efdbaf4cd8c82202a543bbceb02ffc8c", "guid": "bfdfe7dc352907fc980b868725387e98a467f43a870707a46509751f23e132cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98286659341c28b7779f6d13804d445a12", "guid": "bfdfe7dc352907fc980b868725387e9802ea093a5727abf7e5bf2de0a1920819", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846439d1b40bad9d3eb338e756301ff55", "guid": "bfdfe7dc352907fc980b868725387e98a4275a9a8ad93349567e478f538e311e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833e3ce78dfc25b5e485e678ae0fd9e2e", "guid": "bfdfe7dc352907fc980b868725387e98a3b885ca5a4f0f190763f2ad81f80867", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6f1d4962c6d67993f5397c58f6e3eca", "guid": "bfdfe7dc352907fc980b868725387e98f67fa2a3ab51004100222ba50529d624", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c0a72b215e1ee53952f5a627ca6f1b1", "guid": "bfdfe7dc352907fc980b868725387e98a8d0becb193ce52d2b4d8a4a6a7b8b1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f61846505d24f873a39f3da505df33c4", "guid": "bfdfe7dc352907fc980b868725387e9888aa60cb1b0b4448cb7a57449d041d42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60082dfa851b806ea113e8f5b6f8ab4", "guid": "bfdfe7dc352907fc980b868725387e984e50875ec39a261fcdacecb8fd97e6b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982581f96a9548352c7bf3c3405f9b9ff2", "guid": "bfdfe7dc352907fc980b868725387e98680b6d65d40247b63eeadbc0438919b1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b30b2dc8df54cf2331c1f2967903c94d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cba73309d9fcec70f049784c9839c867", "guid": "bfdfe7dc352907fc980b868725387e98be0f815ca1581ad7f68fb576d0814e40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98973ccc166ac370bf1487a2efd56e0177", "guid": "bfdfe7dc352907fc980b868725387e9805a8c147383866ceca1166c8cc95c7d7"}], "guid": "bfdfe7dc352907fc980b868725387e98921b015e930b7f7f41aa588b3f9edcbf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98c168ae529be291352fe61b59a027f545"}], "guid": "bfdfe7dc352907fc980b868725387e98d423f0cfcb5e5c7d08ac5ca4f944cb10", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98413489441bcd17d4df470f6eac68275e", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e986494e886a088b73704c9cd684380afc2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
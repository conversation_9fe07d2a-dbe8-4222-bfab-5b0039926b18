{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bb683da692f76ce9685d04aa4eedfa6e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6767ebf8467dffbc2c62e5ccd4ee603", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e282ee73670975495f930cd4137229e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fdaa9502a46f0ad41a6bfbefa79f140", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e282ee73670975495f930cd4137229e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a6f715905fadeb2867a0648b9c2f93ff", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870d9b3b79f3149d7c874d503aebad746", "guid": "bfdfe7dc352907fc980b868725387e98e0515cce533ced484ef3518e245b5592", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989e3c005a8677e2831ac20f9072f2c917", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9848bc0ab3116d7c915f0c92886c40980e", "guid": "bfdfe7dc352907fc980b868725387e98aadb1e34a1695ea3e9a6b04161620bd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5f51d5bd92e550382019850b97f10c", "guid": "bfdfe7dc352907fc980b868725387e9878aabca4e409a0e37bc4bfeb16128987"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98487811552b5f3e0870fc73cf37ca57cf", "guid": "bfdfe7dc352907fc980b868725387e982c26b853b27e5ecc1e821cd1ce2bcdc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982efbfaebcada5de28d6630242ad17694", "guid": "bfdfe7dc352907fc980b868725387e9865c1e42c2830694feba4b011f3177aa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d09ec09508910f4db990e3abf331e74", "guid": "bfdfe7dc352907fc980b868725387e98d93545e9524f7501ae71cc340695c21c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ada610889c0dd6dbbe6e17f51dde13", "guid": "bfdfe7dc352907fc980b868725387e987614e29e85e0b5dd6b797b7e159fb225"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f73156a1e1d3906586a128b95a7f5ad2", "guid": "bfdfe7dc352907fc980b868725387e98afb715d1d98bcf3386f292fa3b4d5b73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858fc34dc1d04d30df8e272854ce7d688", "guid": "bfdfe7dc352907fc980b868725387e98aedb9427f352bcfcc7dd45b4e51896da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8886634eb91194666ce5bf34df914ea", "guid": "bfdfe7dc352907fc980b868725387e9848cea4ee5402caf7c0f96968e1dedfe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef51dadf04f20fd934cdf6861610ad9e", "guid": "bfdfe7dc352907fc980b868725387e98e0ecbdb12a993bc7bb0c66597f885a0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d997c88bca2cf94be19d37f9210b5b3e", "guid": "bfdfe7dc352907fc980b868725387e985666cb5649ed50ccd71d6fb59ab5ae1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98293fde7e9588e8d5e171a731ac118c72", "guid": "bfdfe7dc352907fc980b868725387e98cd98f34bcda202bf124cf5670d3c2ef0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f404cb7ba720f1d4cd17efcd2c5a275", "guid": "bfdfe7dc352907fc980b868725387e98f6d28e3e6df91a96837e90a3fc80d5e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986235596b49d37c9ec94d94898a2100d1", "guid": "bfdfe7dc352907fc980b868725387e98411e0432a8389935470d0af142ffdb08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b817c9c8f5aed495151afba111acbf9b", "guid": "bfdfe7dc352907fc980b868725387e985f329444375319852a61fdde283560cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62203c488bcbb6eb3d407b3e8434ae8", "guid": "bfdfe7dc352907fc980b868725387e983bab75abe24f637ea97fabcf8a882b19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3e5e9ee0495726e65e02ef863229a7b", "guid": "bfdfe7dc352907fc980b868725387e980e031324bc06be28b1b0ad92b87a0973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881200da389e62a30ea31dd28712645b5", "guid": "bfdfe7dc352907fc980b868725387e98d8d9c057e81648e317237bad488f7b14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98682ad2e6fb665411cd8d17f6b08d136a", "guid": "bfdfe7dc352907fc980b868725387e98c5a0c601d8950aba9c0e9b0e55bd82a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff065f4551188ba28fa8fcda7aeb482e", "guid": "bfdfe7dc352907fc980b868725387e98133d1717d8a6d805d392d8d27d3ce597"}], "guid": "bfdfe7dc352907fc980b868725387e98777aa8cd07db837b5e60eb076a3a656e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98136ee57c415d972876e36519e48bc90d"}], "guid": "bfdfe7dc352907fc980b868725387e9829a9c5578961bc7adb041aa47983ee88", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fe02eb0e8062fd1b5ebee5b1f98171bd", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e983208ae81ca745bd0d5e72e5ba22456b2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
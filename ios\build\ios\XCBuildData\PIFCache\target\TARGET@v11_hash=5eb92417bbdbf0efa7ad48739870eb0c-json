{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fbf9bdd4f71a5bb3042a52b2d0d46112", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9829207d0b50ebb4696abbccdcd410b475", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98923c1b71ed1573983921f545457652f4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820477eb84e3474c6e81b06065e19807f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98923c1b71ed1573983921f545457652f4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989fb4d4190e68c770d7c2a72970a80964", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6511fdae04133f03e0d4b81224546bc", "guid": "bfdfe7dc352907fc980b868725387e98e3006485f146300e2fe90e2f64ed072a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efdbaf4cd8c82202a543bbceb02ffc8c", "guid": "bfdfe7dc352907fc980b868725387e9859fb5a9b90634f673bfcf8551e2756d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98286659341c28b7779f6d13804d445a12", "guid": "bfdfe7dc352907fc980b868725387e986c00ab62d5c62c5c96f1e99845050126", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846439d1b40bad9d3eb338e756301ff55", "guid": "bfdfe7dc352907fc980b868725387e985c027b9cdf574356059b418adb89886a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833e3ce78dfc25b5e485e678ae0fd9e2e", "guid": "bfdfe7dc352907fc980b868725387e98b95f4bbd395b1a638a4924bee32db8ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6f1d4962c6d67993f5397c58f6e3eca", "guid": "bfdfe7dc352907fc980b868725387e98c5141f020ea4606c867b3c7685b39e4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c0a72b215e1ee53952f5a627ca6f1b1", "guid": "bfdfe7dc352907fc980b868725387e98865c33d665e053136b49d4842ef0c52b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f61846505d24f873a39f3da505df33c4", "guid": "bfdfe7dc352907fc980b868725387e98b531f67d34989736ff63fb95f4a37265", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60082dfa851b806ea113e8f5b6f8ab4", "guid": "bfdfe7dc352907fc980b868725387e9853e23e7aff2916de949868af3c6c4e9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982581f96a9548352c7bf3c3405f9b9ff2", "guid": "bfdfe7dc352907fc980b868725387e983d77b996c1bc5090b3f8a1060d9eb1f9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9840d0ab2f494c3fe137c0a1728c951a79", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cba73309d9fcec70f049784c9839c867", "guid": "bfdfe7dc352907fc980b868725387e9870838afe3c8b74f32cd1b33aacc3ab13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98973ccc166ac370bf1487a2efd56e0177", "guid": "bfdfe7dc352907fc980b868725387e9886bca4a6d74b43bc56158f22bd267c5d"}], "guid": "bfdfe7dc352907fc980b868725387e98c954cd7d6b72b19b3f7a69b5c9211fd0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98e524f6cab9c329d1566ccf9a2d79d5eb"}], "guid": "bfdfe7dc352907fc980b868725387e98328dd4928d45747674d2d7437096443a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98475eca2417f67f24aa5e1f2f86ab23a7", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98f7bfeefe4509310f1611d13458f4213a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988db85a4b8d607bd0574ae0b80d7f5ef3", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f2d851ca7170fc3d47e1c6e236cd3d2", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f2d851ca7170fc3d47e1c6e236cd3d2", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846dbd57364e9b9471d1b971703adaa36", "guid": "bfdfe7dc352907fc980b868725387e987b2a96482406cd48d3c5e964dbef8bc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6817cfbcd6f21c12f9d0b004b624fc0", "guid": "bfdfe7dc352907fc980b868725387e9834bcd61548e1006692ffca0df79d05ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a19c2e6fd8119e8ed813a600be2aea0a", "guid": "bfdfe7dc352907fc980b868725387e9826d0a88b076166c4b864cdbdbce92bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846dc198e0d22f126a69491ae1186500a", "guid": "bfdfe7dc352907fc980b868725387e98f06dd451ac8f1a44c57764e553b527a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5cc5435a73072004c264c784e3e3a7f", "guid": "bfdfe7dc352907fc980b868725387e9858166e44940ebd93c8b5a766b12b84f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98376f80b4917343e9af0dfc920f568875", "guid": "bfdfe7dc352907fc980b868725387e985bf0c4cd98e92817abbdcc35bdc5531f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2505105a6c63c5dd13db28e6c2d73d2", "guid": "bfdfe7dc352907fc980b868725387e98c51f26b87df945a97112699464d5bdf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a539a3ece40143c46409f3f2e777fe70", "guid": "bfdfe7dc352907fc980b868725387e98af4c2ceadace42a2f4adc0d63da70812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b680a952b3f6363f4c6a3d4a07e475", "guid": "bfdfe7dc352907fc980b868725387e986dc9311543863ced0c90776181c9ea38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbe8abe8f2ef8991ad1620c917bbf215", "guid": "bfdfe7dc352907fc980b868725387e982c85902d07d31bb1e5ed529b4fec6022", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcfb6735ffe8ca1485bb2d893c18cabd", "guid": "bfdfe7dc352907fc980b868725387e98a00ffc876eed62fce6ba5794f8479b12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b243be213a535e3998663d6f9f600ba3", "guid": "bfdfe7dc352907fc980b868725387e983ad80b28dc127b28a95d9b21b3eba752", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ea9709b399e4b0e95858354d33a20b", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98216478652245564aa06bf13f7d48638c", "guid": "bfdfe7dc352907fc980b868725387e986c3f6d4e621a705b01b32cad0135ff32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98785d0c549f250138e93f1c618eec0cf7", "guid": "bfdfe7dc352907fc980b868725387e98908859acf0314667d813df278ebc7971", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823c065908c4383bad8eb62814adca728", "guid": "bfdfe7dc352907fc980b868725387e9835060e3266401f00c0e55803e560ef3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac8e4f812d5ab275e3093156220ac8d4", "guid": "bfdfe7dc352907fc980b868725387e9892c65e1f99e88c517940337dea3b8c13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e9283985c6d0ce3cf3a4418aceedea", "guid": "bfdfe7dc352907fc980b868725387e9870e3688111116f66aea186a02ca7fedc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805523699b6b69ea15f795c6aa759707a", "guid": "bfdfe7dc352907fc980b868725387e98d8787100502fa9502542bcd3a823833c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ff2dff901c3052559263338fd6aa393", "guid": "bfdfe7dc352907fc980b868725387e988052de86f70283058d324ddc6ab5acdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831834e7ce8d8429773ee834f44b6f976", "guid": "bfdfe7dc352907fc980b868725387e9872a4c5a4082445e692c9d19c755c8ca6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986203d0d74e3b0904b9910dd92d1502e0", "guid": "bfdfe7dc352907fc980b868725387e9804b492d3ba5a3c526fd4ef87a8c1332c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119188f69b5be24496c9aa2f01cb690f", "guid": "bfdfe7dc352907fc980b868725387e98a6052558bee4cf5128d3d0f542c926ed", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9887183ec695a81eb8a9d56213085ccb62", "guid": "bfdfe7dc352907fc980b868725387e9849d0047527a358bfff8af909b731bda0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb4a7387eb46640d226cd9448ca195bb", "guid": "bfdfe7dc352907fc980b868725387e98058b771dd892aad80c349da616705ad7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d7da13ba069cf51b189369fdca84974", "guid": "bfdfe7dc352907fc980b868725387e98a33accb89e3fe72aed974b9e3f2fa683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f5bf0f7841b8a2d8dc9d37da94e8744", "guid": "bfdfe7dc352907fc980b868725387e98531d786c9c0863ff8e634db7caba945e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f673824d0bf1ab5847e10703958e98b", "guid": "bfdfe7dc352907fc980b868725387e9809daffbf8d209e4827b067f350b66ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890a5bc681573f192c18972725f10c806", "guid": "bfdfe7dc352907fc980b868725387e981ec6e69e78436e9f90db8be66c2da384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825e6d533824d490c7bfe2d592877be4d", "guid": "bfdfe7dc352907fc980b868725387e98e87b27bbc76fcba88e2da7f95c269456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fe869fc2e1cd231a940301ccbf9d2ed", "guid": "bfdfe7dc352907fc980b868725387e9898a2705ad2089817ede15eeeeacfb22f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6652a1c1ab490cd307b3b703de8e8c2", "guid": "bfdfe7dc352907fc980b868725387e987b1fe7dcbc4de956ab960b813043de76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df6b55c870c18012a662bedb2d0ef820", "guid": "bfdfe7dc352907fc980b868725387e985e6b60e54a68d747cff302ba76b28f72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c21f9380cdfe01ba70b45c3174420ba2", "guid": "bfdfe7dc352907fc980b868725387e98b02270ba1701e242ede905bb791e4bb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0546c1bec0aa18ab1a0e5f25203c26c", "guid": "bfdfe7dc352907fc980b868725387e98a5300e0a36e0f37f36453390299cc559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee4d38db61f8538dac53ecf56adc5159", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844053c8c0c8da1af9357e2b74d0241f3", "guid": "bfdfe7dc352907fc980b868725387e98187ffa737b89431fd12596f736d5b0ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4b57033cda23da91dce2e75287a48f1", "guid": "bfdfe7dc352907fc980b868725387e98df7b7f5210ab096b5b90dc28d6a1ff40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98739a4b92b42832ca3f7777491de792e9", "guid": "bfdfe7dc352907fc980b868725387e983cfef307d010a7dce17c978e0e3a8387"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98788e424e7423da0b93785d031e2a3ab7", "guid": "bfdfe7dc352907fc980b868725387e98e02cbf019d1e54682905237fda670b58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807362e86ac46a9e47bb776c59cf8fdbd", "guid": "bfdfe7dc352907fc980b868725387e98a58a723c82c2b84fd623236bed4e346b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d77727ca1e65e9f825bfebbb9901f2c5", "guid": "bfdfe7dc352907fc980b868725387e986f4b52efa0acd85e7ad08118a848fa36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f49c376aadb2ae89836289b7813b1e4", "guid": "bfdfe7dc352907fc980b868725387e98fe02c6ba8bbe97e0b7513989d619e5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859778d6b59e6ef8dc3d00b64125aabf8", "guid": "bfdfe7dc352907fc980b868725387e984f1bf9fec22522dbec969a28b45a6acc"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a143de2165105318ef45cdf55486c71b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e9869b52374ef45fea913b8bc4740013", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ed3f2ab4fea5b088206cd73f3fbc968e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98513b43579ca5b52d68247e8a073236f2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ed3f2ab4fea5b088206cd73f3fbc968e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ebc7cd6b7f8754f711aeefffa6cff841", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989594bef04a9df3a888cd90df1d86da6c", "guid": "bfdfe7dc352907fc980b868725387e98c18e0842d2c40be6baf66e601bc6e12b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831fd95278f470dcea1eb3f99623a2e4d", "guid": "bfdfe7dc352907fc980b868725387e987601dfef91e7ce1d3b10e3502ee8eb7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d83fef8309b8b732cdd855b7239566", "guid": "bfdfe7dc352907fc980b868725387e98a95d1b692179f54a286b765b6d792a12", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cf31e89973acdc311708c508cc5451be", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987d46b9f9f5acc760d33424c006fc9eaa", "guid": "bfdfe7dc352907fc980b868725387e98865683e2a0e6f8162a3f992bfe48ee42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15067750f1c3336cc8e40bdc496e947", "guid": "bfdfe7dc352907fc980b868725387e9870cca0583e81992f1dd870014e189905"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd82aba2de67fb50ca80a679cc85fc68", "guid": "bfdfe7dc352907fc980b868725387e989fd63e02e1f43ffa574b964189cdc86a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cac4521387fa7dd508daaa27a9cf7c3", "guid": "bfdfe7dc352907fc980b868725387e9847202728f2df8a905e6bb40dcc998e7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3f21e2ae09fd4dea9b04a85083468cf", "guid": "bfdfe7dc352907fc980b868725387e98464b836c0f16c36da5717005eb248006"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987845404e560d61d5a5d4d838ba43e8f4", "guid": "bfdfe7dc352907fc980b868725387e98528badbadb97d09e02ae2be27aa2791a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98852e8fbaf79899f6ebfefd72795f994d", "guid": "bfdfe7dc352907fc980b868725387e982bc92d49dce1519e323cdb331e61bc6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be5511ebbc6b44593a79e279144631c7", "guid": "bfdfe7dc352907fc980b868725387e981a457a4d5a300bc9729e5b8ef99cc03f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984873d0074b58281da76703f21be49280", "guid": "bfdfe7dc352907fc980b868725387e98089ad0e9a67219d89288afb432c7e65f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dc8c45c0ca11f536fa71c5125ba907d", "guid": "bfdfe7dc352907fc980b868725387e98593f8f6cf6a3e67556dfb6cf815c6eb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8c0faf149a3e3b1ae9cb0bc57e94e6", "guid": "bfdfe7dc352907fc980b868725387e987bf3e7fa5518acc7369b3a6082a69da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7deddd9a9ace33e80bc880f6d7cecd", "guid": "bfdfe7dc352907fc980b868725387e983a4b7293e689ce82bbd7f26645f76072"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff6448d04c50a10a778f6255c5371a99", "guid": "bfdfe7dc352907fc980b868725387e98b8186d34476fff0eb2f5530e5ce4f7bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841dbe4a3be4d52ecd10cb142fbc5c4a5", "guid": "bfdfe7dc352907fc980b868725387e982df9bc7f76cbf4659df9fa87a0f1b719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98887936770ec62047a5d0b18a3ebd0fca", "guid": "bfdfe7dc352907fc980b868725387e980eabb2c644a6982db2112ca32e1f5f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fcbbec1154497fc970a06583633b4ec", "guid": "bfdfe7dc352907fc980b868725387e9809bb0a3a568bff33430f8b191f2f1561"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f52164808cf7f8dcbb8f8190a5b79e7", "guid": "bfdfe7dc352907fc980b868725387e9867c7c0eae2f251e472f813d29a5761cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985edfa1dfe2f567e0d761ee26d17ca709", "guid": "bfdfe7dc352907fc980b868725387e9826908d1f4d334f6e89ee69df447519cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2e645fbcca42a9c13a48fdb8ceab5f3", "guid": "bfdfe7dc352907fc980b868725387e98ac4874c266ed3107ad49a8c834c70fbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b70ddd99805dcaa4cd0829adf858e176", "guid": "bfdfe7dc352907fc980b868725387e982fcd379429d748f8430cc47eadb52159"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c457a4ad67ae64bc7108d7309f7e07d3", "guid": "bfdfe7dc352907fc980b868725387e98923dd4cc0aace7e007c6e9fc40a8d4ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822958f9737a30c101c26957e82461659", "guid": "bfdfe7dc352907fc980b868725387e98cac33df2b11a4f46aeded5c7776dda48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cfce1473be04651645f7958f9d43f7a", "guid": "bfdfe7dc352907fc980b868725387e98ede18daaf38ac4cf3cd36c8a4e8ef8c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba9d1f5064826b7c5145a1d1d9465ca", "guid": "bfdfe7dc352907fc980b868725387e98f89cc05365239b9782cc12d29996792d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3a5620cdde3e402a0204f39a93c0962", "guid": "bfdfe7dc352907fc980b868725387e98ceeb3e188872e41c5ad9649e2b0e82ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e65db4f90675138a792a02f7e2b816f2", "guid": "bfdfe7dc352907fc980b868725387e983bdce256c29e878e28bb98d6731222ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983363ec6ebeef2a2a98645cea65a3be84", "guid": "bfdfe7dc352907fc980b868725387e982b8d1d54671b9723bc6a63f40ead1d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd2cade2fd158454ddb9d7a51ccc99d2", "guid": "bfdfe7dc352907fc980b868725387e98d05666c099a1dd72521627d365763377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e393f68b5f753a2f5c80488ec767033", "guid": "bfdfe7dc352907fc980b868725387e9829c075d365d4d6ec12a60aa4e5da84ea"}], "guid": "bfdfe7dc352907fc980b868725387e98aac32b519109fbdcfaa3a74cfab2e06a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98516eab448bf9d9014aef8d22243a3baa"}], "guid": "bfdfe7dc352907fc980b868725387e98afffbcf7ec3236730f8a837bdadb31cb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98806573bab0d26ab26d339eff62a15778", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
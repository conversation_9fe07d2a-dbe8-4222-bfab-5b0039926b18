{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bbb8a9f2908ccb16a5669503da3350d1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986298660b805aa2d4dda093737e1ff441", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808d5923f2056835192e8ae0f75026ad5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824d0a013085a9da60f1b19862788179b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808d5923f2056835192e8ae0f75026ad5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c27485fde666964c5a5ab0cc332a0416", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf07f516144fdd964ccbaa32bfee928f", "guid": "bfdfe7dc352907fc980b868725387e988688c6c98a19cfc461219d18672b23a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd03c05b03cb8ea7cdf7d49443a8790", "guid": "bfdfe7dc352907fc980b868725387e98567cd91f8824ee4942eefc04cb749df9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13cc343645a4c4a8723836e182e8c7b", "guid": "bfdfe7dc352907fc980b868725387e982e35264a48b36ae31e7b17a24907ab21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824de7bf9d3983914c90378e1ccf725ea", "guid": "bfdfe7dc352907fc980b868725387e987912db6a2c40c9f943683f1d71577095", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bff5997e882e8351916a1470d0c4564", "guid": "bfdfe7dc352907fc980b868725387e988cd2eb4b7dc29043c76737787894fc0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d443d94e3b0f83e24fd777989dbbc852", "guid": "bfdfe7dc352907fc980b868725387e9806c4c11bdb72ca7d120df6ddc388b66f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4151da1941c6d83484ad2c3206aea16", "guid": "bfdfe7dc352907fc980b868725387e9863448691044074764d11f55196910ab5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28087c9e505a8aa23bf57d6b34643d3", "guid": "bfdfe7dc352907fc980b868725387e98c58c458eef608dd25db8688f8c5fbc73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dce96620652bafd898ab6e2e307c915", "guid": "bfdfe7dc352907fc980b868725387e985b261bea7d4da4a12a79206c9153b364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817203d2ef34142d6d74046d55dbfd2fb", "guid": "bfdfe7dc352907fc980b868725387e98b6b86f497bb64c14669458383e02c061", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988951d0b55186a56c60dcf9dcb9ddc82a", "guid": "bfdfe7dc352907fc980b868725387e986dfc8512f488c0f6b6f16b6d82b74d25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff3820999ec12a7db0cf3a8c4f55be3", "guid": "bfdfe7dc352907fc980b868725387e98a964da09a716de170779e9a823c4fe5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98598bb75d9f254478b77e0873dfc6ffd1", "guid": "bfdfe7dc352907fc980b868725387e98981e03f01f1d00567721b4bd58b92f8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345449fa4072399a6253833d3de405d8", "guid": "bfdfe7dc352907fc980b868725387e98921cd61a253a90059500c4b9b5833436", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f9e9e6a9810de32b23a9c276492960", "guid": "bfdfe7dc352907fc980b868725387e98beecc57af03a748f3e09621d6ce8594b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b6bdce9fbd377335ebbc285511e5bfd", "guid": "bfdfe7dc352907fc980b868725387e98d9342dc11345d97e42f43ee25549b523", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6a0ba2ddc7887d740ab01532fb0caa5", "guid": "bfdfe7dc352907fc980b868725387e9844c35ed6fa6caeb61f3736082116ba04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e287e32f78a316fef056e2d71cadae", "guid": "bfdfe7dc352907fc980b868725387e98509724774d5bdcec2488d212a5654c4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835f1bf1b1b1e444d931f98824af7f3fa", "guid": "bfdfe7dc352907fc980b868725387e9844480d2c37166f75f1e3f85dd2c2aeef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7820ce811cad8bd315e5117679d6312", "guid": "bfdfe7dc352907fc980b868725387e98eb470e348fb1f8d5d1617ab723412d09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828d619024d60355045198dad7d13bfc9", "guid": "bfdfe7dc352907fc980b868725387e98916553a19ab63e7508b0e5c40421641b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809964c30f05d00cde67aa662af1626b1", "guid": "bfdfe7dc352907fc980b868725387e98d35978762d737eada58ea207b1584de1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1cd19cb6da485c9f56d05a672e5cc20", "guid": "bfdfe7dc352907fc980b868725387e98a5788c5da79aeb6970442fe31844a1fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985786b7d05930b512791eaa0a97278da2", "guid": "bfdfe7dc352907fc980b868725387e98fc1d83e668eaea46e44bca7e0d274af0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e7caa55e314b1e772ffc5513b8d9023", "guid": "bfdfe7dc352907fc980b868725387e98c88ddc8ba2cc088ac84b1b171e570538", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c4e5aac4ebc143b18b6ad9fdcdbcd8", "guid": "bfdfe7dc352907fc980b868725387e98e040b99e19c4746c8dfe66e8cbaff35e"}], "guid": "bfdfe7dc352907fc980b868725387e989fba17201a2efa21efb29e3e66a9fd61", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f0ff927e31de690b562d742c35a04952", "guid": "bfdfe7dc352907fc980b868725387e98f6ce55f45fae23c6ff507f7f3755b65c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ecfc930fef1057c6ab64a9bc0d576e", "guid": "bfdfe7dc352907fc980b868725387e9875b94b30839cf02b1191026723065f08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c27ef2284bada3178a499187737f1bae", "guid": "bfdfe7dc352907fc980b868725387e9879bc18719f29f221b85dcfb94b52db62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b1cfb6077130cb8ec388164e6824153", "guid": "bfdfe7dc352907fc980b868725387e987c956f831c329eee7beaf015bfcf44e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d70df0567b7b8d3fef7bf8683a408c49", "guid": "bfdfe7dc352907fc980b868725387e98e197581330a9cd478dfc8daf95e817a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2b42bef276761a8c8590081bfae1a9", "guid": "bfdfe7dc352907fc980b868725387e98489a8357b6200b390f1caee49642c6df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d816f2622f4863635b0483027d84d49e", "guid": "bfdfe7dc352907fc980b868725387e9803fa174a24382cdfa886c6cdd89cbacf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818865f4cec7494103a34bc0727edd33f", "guid": "bfdfe7dc352907fc980b868725387e989539db82eabad7bcccdf7ea50ba6ad61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879300f85cbc276809b5c577bc4ee1ad6", "guid": "bfdfe7dc352907fc980b868725387e98310ea0461923a3c7db29e0a19534e721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c07f7573b2bfa2a08809b65b6892255", "guid": "bfdfe7dc352907fc980b868725387e98594705e3cfb95499b6f222b9ffdae310"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98540505e4c6c928c81e8e36f98fe82211", "guid": "bfdfe7dc352907fc980b868725387e98d868e94db8473c95ed3ae4e479fd619c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de806e0fa57c5b9e6b949687b23e1c74", "guid": "bfdfe7dc352907fc980b868725387e98ea68b76ab049f2f6eda407de6aebd81c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f39bf94fabc0ef09108922b302529822", "guid": "bfdfe7dc352907fc980b868725387e98d5bd8ba249685d70687a400a31640991"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3613dc3e2c771d062207071d0cbd45f", "guid": "bfdfe7dc352907fc980b868725387e9897e7f8943b661b84ee7ed4eb390adffa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b7e96c4a57f2ce30a3384313998fc8", "guid": "bfdfe7dc352907fc980b868725387e9896943c0925d5d341038b7e0240609fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830a940b45c68c90745493f0224db3233", "guid": "bfdfe7dc352907fc980b868725387e981b54ab018c3af0b6c43a47fe84e8a71f"}], "guid": "bfdfe7dc352907fc980b868725387e9836338846ed5821ef2d09b1a907945856", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98a92cd8b7d33d71e1c5bd9a57ac0a3a43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e98798a8da4976e23fc3cc014271a936a1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f0d1dd6b952d008563ab75cee403874", "guid": "bfdfe7dc352907fc980b868725387e988136e9feef9d23c4599fa69945b011fa"}], "guid": "bfdfe7dc352907fc980b868725387e987924a74b23f192a543c0f015b8e1666a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d2d73e271ea5b98c2b51fce7214dbee8", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98ea1befe096442a200d7b91bacddaf47e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
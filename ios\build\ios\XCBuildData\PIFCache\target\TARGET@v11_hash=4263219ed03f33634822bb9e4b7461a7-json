{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bb683da692f76ce9685d04aa4eedfa6e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987017e3eb76cf18d82223a72436324c19", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e282ee73670975495f930cd4137229e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984b87231311c6db0a55341c267823fe27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e282ee73670975495f930cd4137229e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980753b4a9f66ed306c75440cee7e46803", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870d9b3b79f3149d7c874d503aebad746", "guid": "bfdfe7dc352907fc980b868725387e982c3479ac09f218a7a45ff810fe5dd0cc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9868a41161ff9c48f045595724ba9d256d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9848bc0ab3116d7c915f0c92886c40980e", "guid": "bfdfe7dc352907fc980b868725387e986b26c009282e9557b577087c19961948"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5f51d5bd92e550382019850b97f10c", "guid": "bfdfe7dc352907fc980b868725387e984017ab93902d0484e3f0716ca0daad19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98487811552b5f3e0870fc73cf37ca57cf", "guid": "bfdfe7dc352907fc980b868725387e9833f2c05f73b1c2a2bc049612cf485afc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982efbfaebcada5de28d6630242ad17694", "guid": "bfdfe7dc352907fc980b868725387e9871ce3ddd7823abb6c0dc5137bb2af648"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d09ec09508910f4db990e3abf331e74", "guid": "bfdfe7dc352907fc980b868725387e98f53968c820a1079c0605b4692246320f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ada610889c0dd6dbbe6e17f51dde13", "guid": "bfdfe7dc352907fc980b868725387e985139d817352655ee851d268ac73ba608"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f73156a1e1d3906586a128b95a7f5ad2", "guid": "bfdfe7dc352907fc980b868725387e98f8398ad86afd549626e01d4e25cec9ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858fc34dc1d04d30df8e272854ce7d688", "guid": "bfdfe7dc352907fc980b868725387e980496db42b5d361a1304358df5a4f14d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8886634eb91194666ce5bf34df914ea", "guid": "bfdfe7dc352907fc980b868725387e98f77ce646463657d98dd77180648d9e17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef51dadf04f20fd934cdf6861610ad9e", "guid": "bfdfe7dc352907fc980b868725387e98df3eac1fa0fc12b6f4f9f3075ae464b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d997c88bca2cf94be19d37f9210b5b3e", "guid": "bfdfe7dc352907fc980b868725387e98cc84d2a7ca1fa763829bc7209ebbeaac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98293fde7e9588e8d5e171a731ac118c72", "guid": "bfdfe7dc352907fc980b868725387e98f97d560df084c468a00eef09c15f3ac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f404cb7ba720f1d4cd17efcd2c5a275", "guid": "bfdfe7dc352907fc980b868725387e98cd71b93da78a8ceb06033c12efdb415d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986235596b49d37c9ec94d94898a2100d1", "guid": "bfdfe7dc352907fc980b868725387e98e7f5d5989b523b6bd47a61afcefbe89f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b817c9c8f5aed495151afba111acbf9b", "guid": "bfdfe7dc352907fc980b868725387e98b757876d7096caf88d78fcaceca2486e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62203c488bcbb6eb3d407b3e8434ae8", "guid": "bfdfe7dc352907fc980b868725387e9842a515809c264410dad896f7b67b55e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3e5e9ee0495726e65e02ef863229a7b", "guid": "bfdfe7dc352907fc980b868725387e987f4465d93118af6b710f5cf114f9f001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881200da389e62a30ea31dd28712645b5", "guid": "bfdfe7dc352907fc980b868725387e985bb1c0818f3167f4f63bf096f194d2fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98682ad2e6fb665411cd8d17f6b08d136a", "guid": "bfdfe7dc352907fc980b868725387e9815cc237bd60e16eafccb764d6e184cfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff065f4551188ba28fa8fcda7aeb482e", "guid": "bfdfe7dc352907fc980b868725387e9819ef289c55ffe5bf8d7da4af2aa38708"}], "guid": "bfdfe7dc352907fc980b868725387e98f029514d5c6668ccaa4decf07e04d26b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98173ea4bf9ca9af683a8dbceffdea86bb"}], "guid": "bfdfe7dc352907fc980b868725387e9810bd3c00363b498fe09194641ecfd560", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9877c1fc986df170f3c7039dc56fc36d97", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e98b63fba53e727b847352af8fccfa3402d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98adbde50afa8304d92c5c8e69f02c1c4d", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e983bb9ad0aca925303414363fadddc1a15", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98312724f33174849ecbb70c98e9ce3618", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98d61111266bcf2ef79ea7514dbacde9ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98312724f33174849ecbb70c98e9ce3618", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e984a7fdf2634bb70fe9d778e1c0fc05d75", "name": "Release"}], "buildPhases": [{"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "bfdfe7dc352907fc980b868725387e98c5c2fad06eb917628adf73716a65585c", "inputFileListPaths": ["${PODS_ROOT}/Target Support Files/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics-xcframeworks-input-files.xcfilelist"], "inputFilePaths": [], "name": "[CP] Copy XCFrameworks", "originalObjectID": "7E197A9CF939C5021C33E2E60B44344C", "outputFileListPaths": ["${PODS_ROOT}/Target Support Files/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics-xcframeworks-output-files.xcfilelist"], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "\"${PODS_ROOT}/Target Support Files/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics-xcframeworks.sh\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98f52a87663484c8c5c2fb376dbe85216c", "name": "FBSDKCoreKit_Basics", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 0}], "type": "aggregate"}
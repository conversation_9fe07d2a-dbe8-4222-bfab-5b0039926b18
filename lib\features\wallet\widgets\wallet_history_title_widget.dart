import 'package:flutter/material.dart';
import 'package:hexacom_user/common/widgets/filter_icon_widget.dart';
import 'package:hexacom_user/features/wallet/controllers/wallet_controller.dart';
import 'package:hexacom_user/features/wallet/domain/models/wallet_transaction_model.dart';
import 'package:hexacom_user/features/wallet/widgets/wallet_filter_bottom_sheet_widget.dart';
import 'package:hexacom_user/helper/responsive_helper.dart';
import 'package:hexacom_user/localization/language_constrants.dart';
import 'package:hexacom_user/utill/dimensions.dart';
import 'package:hexacom_user/utill/styles.dart';
import 'package:provider/provider.dart';

class WalletHistoryTitleWidget extends StatefulWidget {
  const WalletHistoryTitleWidget({
    super.key,
  });

  @override
  State<WalletHistoryTitleWidget> createState() => WalletHistoryTitleWidgetState();
}

class WalletHistoryTitleWidgetState extends State<WalletHistoryTitleWidget> {

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final WalletProvider walletProvider = Provider.of<WalletProvider>(context, listen: false);
    final Size size = MediaQuery.of(context).size;

    if(ResponsiveHelper.isDesktop(context) && walletProvider.isOverlayShowing){
      walletProvider.hideOverlay();
    }

    return Consumer<WalletProvider>(builder: (context, walletProvider, child) {
      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).hintColor.withValues(alpha: 0.04),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 2)
            )
          ]
        ),
        padding: EdgeInsets.only(bottom: Dimensions.paddingSizeExtraSmall),
        child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text(getTranslated('wallet_history', context), style: rubikMedium.copyWith(fontSize: Dimensions.fontSizeLarge)),


          if(ResponsiveHelper.isDesktop(context))
            FilterIconWidget(
              onTap: ()=> walletProvider.showOverlay(size, context),
              filterCount: _getFilterCount(context),
              height: Dimensions.paddingSizeDefault,
              width: Dimensions.paddingSizeDefault,
            )
          else
            FilterIconWidget(
              filterCount: _getFilterCount(context),
              height: Dimensions.paddingSizeDefault,
              width: Dimensions.paddingSizeDefault,
              onTap: (){
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Theme.of(context).cardColor,
                  builder: (_) => WalletFilterBottomSheetWidget(),
                );
              },
            ),
        ]),
      );
    });
  }

}

int _getFilterCount(BuildContext context) {
  int count = 0;
  WalletTransactionModel? walletTransactionModel = Provider.of<WalletProvider>(context,listen: false).walletTransactionModel;
  if (walletTransactionModel == null) return 0;

 if(walletTransactionModel.filters?.filterBy != null){
   count++;
 }

 if(walletTransactionModel.filters?.startDate != null || walletTransactionModel.filters?.endDate != null){
   count++;
 }
 return count;
}
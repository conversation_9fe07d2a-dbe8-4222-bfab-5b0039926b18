{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f145447222b600caddedfab5376486a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980badbf6c76699e22f4e4848a4a343368", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2eb5a856f8176250a0237adf959c7bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981740066a5f442e487570cda64d4bc31b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2eb5a856f8176250a0237adf959c7bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f839cd1b681e57031673db43344f7bd1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c3936c8c76da33e66715e2c1931bd30e", "guid": "bfdfe7dc352907fc980b868725387e98ab13aa5b44a8de314041836b5ef5cc9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7e7704004a718ed06297e4b9f595dc6", "guid": "bfdfe7dc352907fc980b868725387e98bda12bfc4c2779c6335778919449c6b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98025e67aa28734836b1acf8d1174b5d4f", "guid": "bfdfe7dc352907fc980b868725387e985a8c07f8a10cd52e1791587ba338006b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98121c0268d78a0b5da361f24233803dbd", "guid": "bfdfe7dc352907fc980b868725387e98d6092f68df1e1589323fa0dee42c28e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa33485997b400dfba66fb82e8309f01", "guid": "bfdfe7dc352907fc980b868725387e98b9e7ca01cf191867c7141b8c32950162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98665a6c7720e130cc92b2b6953961508c", "guid": "bfdfe7dc352907fc980b868725387e98c30bc10be691c3b43ff4613a44f0e3dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee40685da49f82a3c5f9c1b2031f461", "guid": "bfdfe7dc352907fc980b868725387e987ffdbbf85c98029daace150632b7f149", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc53381ae5cb76197fd315b4e458e33c", "guid": "bfdfe7dc352907fc980b868725387e98e55e55a664d8f75644b4806538b472d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4f153f399471e57c3007098dc218b7", "guid": "bfdfe7dc352907fc980b868725387e988b69abac459d337f4c462e5aa541371a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dcbe6f9b6cd3080fb92cad1191c1711", "guid": "bfdfe7dc352907fc980b868725387e987ef3dab7792da7c6a5e6278b6d11e42e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f19af1b0c820b22ee095f510463879", "guid": "bfdfe7dc352907fc980b868725387e98e6d1d031e32b73e48b41746d40c411e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf5e93f4b6e3ddfcbb16d540f256f41", "guid": "bfdfe7dc352907fc980b868725387e98a1456046f521b9ccb5ecaee747e182be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98422fd92327b9d1928653bb81cb72dd37", "guid": "bfdfe7dc352907fc980b868725387e98faf5a087d632f0bca6012e21bc9f6d0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c753bf472912fae1ec618e5a444855fe", "guid": "bfdfe7dc352907fc980b868725387e9881bc8ef839745b6ca73dd49325597918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39643a9af69fecd5b4af1c967b6cbab", "guid": "bfdfe7dc352907fc980b868725387e9862d8a1fe90b21ea2eb7da1a43e07647d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98928015e7a3110db07cd4d983ad8c649e", "guid": "bfdfe7dc352907fc980b868725387e981e720a9f9f051522590e203ab354dfce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d0dc378b74d2729df3575569a9932e5", "guid": "bfdfe7dc352907fc980b868725387e98965c9806fdd329b583e8e491b8347eec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f65147a5bc023f93052a1e63cb9d9c6", "guid": "bfdfe7dc352907fc980b868725387e98134df390413c24adb2ff96dac97ba5dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893e8c6485a4fe01e78b5ab7ac48c8ad2", "guid": "bfdfe7dc352907fc980b868725387e98e02eae0c9cc7a41b02e921c55ec2f329"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d81d1b6c4d8aaf39d320211f5742bd1", "guid": "bfdfe7dc352907fc980b868725387e980412959680164c4f442cdea1e44d4ea4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983552829699d2b9cd2773144568287423", "guid": "bfdfe7dc352907fc980b868725387e985b59fff827a8a6079c6d86413ef49d8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df13021363282bf055fe1e0e6f8f5810", "guid": "bfdfe7dc352907fc980b868725387e98867820d139e9d0ce217f8ba8b0aa4e1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98244e18e88420eb43cf70ec2a61213b49", "guid": "bfdfe7dc352907fc980b868725387e98c046beeba27129379f26062612dd4217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f178747de923a7ac9f73edf476557a", "guid": "bfdfe7dc352907fc980b868725387e9856214e1e79dd8e06e751cf1cec10bcfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea16329af38b01e45d8c60fa21b2753", "guid": "bfdfe7dc352907fc980b868725387e981140fd827dd2e572b392250028527bfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987342ead266053d7cfe62f370101af059", "guid": "bfdfe7dc352907fc980b868725387e986b09ad1aab159d5237234f87d77a35aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f36ee075be17537feaccbba3c4b48591", "guid": "bfdfe7dc352907fc980b868725387e98cf7c97c5c4f03d7ab46427f7177a4c18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fef013791f30e0bb669bec613da2795d", "guid": "bfdfe7dc352907fc980b868725387e9855e2bcb68431acacec17e4e5bceb4f74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b1e818bf9c57936293102b7889b6728", "guid": "bfdfe7dc352907fc980b868725387e982973e6b5f505fc39a5d8ab054e5ab8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f15dafd6583ca9ebc5957df4603fb2", "guid": "bfdfe7dc352907fc980b868725387e981f262bfb4ade3f20ba148c710be3e068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e1fdef953143c8c457054039f9f9e2", "guid": "bfdfe7dc352907fc980b868725387e981d12f433b9583f9ff2e95aa078d0112e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b647d4cbbdf70e36c6ab4bb7346eb80", "guid": "bfdfe7dc352907fc980b868725387e98c1884c56f3acc083c321e4d8faaec51b"}], "guid": "bfdfe7dc352907fc980b868725387e98fe8b15f78d09570e96170e156bfa2b81", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984779792658a77cd2531e1dc5b6ee4baa", "guid": "bfdfe7dc352907fc980b868725387e9857e131e3ef047dc387588e7d2b051ce6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475a134910b5f1d1511facf92383244f", "guid": "bfdfe7dc352907fc980b868725387e981a554c189a0d3cfce90244169a034f7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836bab5a1ce2824e4b5204d198c195f4a", "guid": "bfdfe7dc352907fc980b868725387e984269d99408e9b62c04d46e747931fa78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853c55ba6ef898bfea49d53c84d93d58d", "guid": "bfdfe7dc352907fc980b868725387e980237e3e5634957d29a4a9072caf7afa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e96fb517fe2ba1c5131cc060f633b1e", "guid": "bfdfe7dc352907fc980b868725387e987c3d22804a2e076418ee075d6c469493"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9e3503a66719a7cb57a8bb321afae90", "guid": "bfdfe7dc352907fc980b868725387e989d3fc7bd98e4239984b969e107c8f09d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449ab16aef5f356c2898b72ea72b1874", "guid": "bfdfe7dc352907fc980b868725387e985e811c5e174627ef46323c86098dd472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd6e54bc17822720bdb533668c80a33a", "guid": "bfdfe7dc352907fc980b868725387e980c0c2fd25bdeffe0b9d0950f247a7688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b3f37f9d71fee583cdbf287b81d82a7", "guid": "bfdfe7dc352907fc980b868725387e988e471e80da491b789bdc271c295e9c09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a8fd24f75912535034806fc092e64b5", "guid": "bfdfe7dc352907fc980b868725387e98136e4c6d667c87b8a46193713e1d63ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbaccfc57198d69f6708448fd1d48289", "guid": "bfdfe7dc352907fc980b868725387e985d6f96860d26dd1ee49555d41da4f326"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883193d564bd602e1db5ffc0cc0bd4b23", "guid": "bfdfe7dc352907fc980b868725387e98f9befd457f6bf6bccdc31adee4a34a14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f42e17f4ed6612a066edfccbdc07f12", "guid": "bfdfe7dc352907fc980b868725387e9870e2e261def201f3920ef36f9e080fcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b2ce22624b6572dd299e072a3e94f95", "guid": "bfdfe7dc352907fc980b868725387e9870c90fd75f824ad5686f4d2bb893b93a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98105da323d7013123ecb286baff1cc8b1", "guid": "bfdfe7dc352907fc980b868725387e98989ab4ff6b1b86a2739762639e71562d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824f2e5c64afaad6d1616bd0dce7e179c", "guid": "bfdfe7dc352907fc980b868725387e98039bcb2d00180346d6050f6175b36103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a03a6aa3aff83d3d472ccdb8fb3a47b", "guid": "bfdfe7dc352907fc980b868725387e98074e2628ee226f068675a456c02def72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ff4929a6cefa0efb01a7225ff50a8c", "guid": "bfdfe7dc352907fc980b868725387e9805116f84b6e7ba991d170a48525e06a7"}], "guid": "bfdfe7dc352907fc980b868725387e9888c03fbc5782ee44f6c0baecd1efa002", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e9857815d8b722ce691726ec14019659347"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e98627323d5b9ef1f864e4b3f0d9632cdda"}], "guid": "bfdfe7dc352907fc980b868725387e98a7e05de1e7873ab2c9ca1b1427be9c6e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ad3a27c4e679a5184e20f885af79037a", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98fbb3219d1aa07aa92a3f2518e66ebcdf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
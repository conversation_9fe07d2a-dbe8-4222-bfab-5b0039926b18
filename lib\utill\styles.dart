import 'package:hexacom_user/utill/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:hexacom_user/utill/dimensions.dart';

const rubikRegular = TextStyle(
  fontFamily: AppConstants.fontFamily,
  fontSize: Dimensions.fontSizeDefault,
  fontWeight: FontWeight.w400,
);

const rubikMedium = TextStyle(
  fontFamily: AppConstants.fontFamily,
  fontSize: Dimensions.fontSizeDefault,
  fontWeight: FontWeight.w500,
);

const rubikSemiBold = TextStyle(
  fontFamily: AppConstants.fontFamily,
  fontSize: Dimensions.fontSizeDefault,
  fontWeight: FontWeight.w600,
);

const rubikBold = TextStyle(
  fontFamily: AppConstants.fontFamily,
  fontSize: Dimensions.fontSizeDefault,
  fontWeight: FontWeight.w700,
);
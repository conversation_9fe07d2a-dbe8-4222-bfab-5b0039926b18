import 'package:flutter/material.dart';
import 'package:hexacom_user/provider/theme_provider.dart';
import 'package:hexacom_user/utill/dimensions.dart';
import 'package:provider/provider.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

class TransactionShimmer extends StatelessWidget {
  const TransactionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return  ListView.builder(
      shrinkWrap: true,
      reverse: true,
      itemCount: 10,
      itemBuilder: (context, index) {
        final Color baseColor = Provider.of<ThemeProvider>(context).darkTheme
            ? Theme.of(context).primaryColor.withValues(alpha: 0.05)
            : Theme.of(context).hintColor.withValues(alpha: 0.2);

        return _TransactionShimmerWidget(baseColor: baseColor);
      },
    );
  }
}

class _TransactionShimmerWidget extends StatelessWidget {
  const _TransactionShimmerWidget({
    required this.baseColor,
  });

  final Color baseColor;

  @override
  Widget build(BuildContext context) {
    return Shimmer(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: Dimensions.paddingSizeSmall),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(children: [
              // Left Column (Expanded)
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Amount Row
                    Row(
                      children: [
                        // Icon placeholder
                        Container(
                          height: Dimensions.paddingSizeDefault,
                          width: Dimensions.paddingSizeDefault,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: baseColor,
                          ),
                        ),
                        const SizedBox(width: Dimensions.paddingSizeExtraSmall),

                        // +/- sign placeholder
                        Container(
                          height: 16,
                          width: 10,
                          color: baseColor,
                        ),

                        // Amount placeholder
                        Container(
                          height: 16,
                          width: 60,
                          color: baseColor,
                        ),
                      ],
                    ),
                    const SizedBox(height: Dimensions.paddingSizeExtraSmall),

                    // Transaction type placeholder
                    Container(
                      height: 14,
                      width: 150,
                      color: baseColor,
                    ),
                  ],
                ),
              ),

              // Right Column
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Date placeholder
                  Container(
                    height: 12,
                    width: 80,
                    color: baseColor,
                  ),
                  const SizedBox(height: Dimensions.paddingSizeExtraSmall),

                  // Credit/Debit placeholder
                  Container(
                    height: 14,
                    width: 50,
                    color: baseColor,
                  ),
                ],
              ),
            ]),

            Padding(
              padding: const EdgeInsets.only(top: Dimensions.paddingSizeSmall),
              child: Divider(
                thickness: 0.4,
                color: Theme.of(context).hintColor.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

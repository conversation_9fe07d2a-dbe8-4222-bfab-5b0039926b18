{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f145447222b600caddedfab5376486a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987322c02b3b9f55d017f032ec37b466f2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2eb5a856f8176250a0237adf959c7bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9870456cf4e94b07e1306fa3b72423599e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2eb5a856f8176250a0237adf959c7bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818da4e8c78b78a906fd8e31c7b39f7bb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c3936c8c76da33e66715e2c1931bd30e", "guid": "bfdfe7dc352907fc980b868725387e98e1a5b11f20ea2e07618e81dc1dd31c79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7e7704004a718ed06297e4b9f595dc6", "guid": "bfdfe7dc352907fc980b868725387e98ad7765cc9d007488eaa37b395d2f58b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98025e67aa28734836b1acf8d1174b5d4f", "guid": "bfdfe7dc352907fc980b868725387e98f6d9feeb7fa7b29f1ef204d9e3be5944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98121c0268d78a0b5da361f24233803dbd", "guid": "bfdfe7dc352907fc980b868725387e98e93042d50371d1d8c2b756a44fc1b039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa33485997b400dfba66fb82e8309f01", "guid": "bfdfe7dc352907fc980b868725387e98dfdc68491f0932d0e6c33785a4685d1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98665a6c7720e130cc92b2b6953961508c", "guid": "bfdfe7dc352907fc980b868725387e98cc3ec5cd9b1d8545e4ae9a1529f52143"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee40685da49f82a3c5f9c1b2031f461", "guid": "bfdfe7dc352907fc980b868725387e989ced3f62adb663fe8685ed59f4b8949f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc53381ae5cb76197fd315b4e458e33c", "guid": "bfdfe7dc352907fc980b868725387e9811ec2177454d0b8619e28253b09ed6c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4f153f399471e57c3007098dc218b7", "guid": "bfdfe7dc352907fc980b868725387e98ac01379d607230837539f8c8b0c7ebf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dcbe6f9b6cd3080fb92cad1191c1711", "guid": "bfdfe7dc352907fc980b868725387e9839596cdbf3e233d6c7a6d3548b4acc76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f19af1b0c820b22ee095f510463879", "guid": "bfdfe7dc352907fc980b868725387e9834d4e63bb9005acefa7b60cf07977b09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf5e93f4b6e3ddfcbb16d540f256f41", "guid": "bfdfe7dc352907fc980b868725387e9859dc66d27e0c3b51c07033619b25134d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98422fd92327b9d1928653bb81cb72dd37", "guid": "bfdfe7dc352907fc980b868725387e98c591ff901f80a8ca8eede998fc9b3f27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c753bf472912fae1ec618e5a444855fe", "guid": "bfdfe7dc352907fc980b868725387e989d6c321c6863e804bfce9943b1f56cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39643a9af69fecd5b4af1c967b6cbab", "guid": "bfdfe7dc352907fc980b868725387e9822a97d8a2ceba48907e3c0ae99f13f4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98928015e7a3110db07cd4d983ad8c649e", "guid": "bfdfe7dc352907fc980b868725387e984e7724930bcbec163fa7551ae3870477", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d0dc378b74d2729df3575569a9932e5", "guid": "bfdfe7dc352907fc980b868725387e98c648afc37110968acfc1a561c0f819aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f65147a5bc023f93052a1e63cb9d9c6", "guid": "bfdfe7dc352907fc980b868725387e98c3f14650f12d195593f81cbe49895ef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893e8c6485a4fe01e78b5ab7ac48c8ad2", "guid": "bfdfe7dc352907fc980b868725387e9875c340267405e1aac82c331dd00c76a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d81d1b6c4d8aaf39d320211f5742bd1", "guid": "bfdfe7dc352907fc980b868725387e987a6b4da1d1ef4348aaa8bd1b3440ec0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983552829699d2b9cd2773144568287423", "guid": "bfdfe7dc352907fc980b868725387e98824b941f2eb66ce0c5f22d0835762651"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df13021363282bf055fe1e0e6f8f5810", "guid": "bfdfe7dc352907fc980b868725387e989692432377a72fd93fb55b05ea6cdf42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98244e18e88420eb43cf70ec2a61213b49", "guid": "bfdfe7dc352907fc980b868725387e98834f5a2bc8ce3ab29be140921271a6c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f178747de923a7ac9f73edf476557a", "guid": "bfdfe7dc352907fc980b868725387e9818252d961567f5cf0afd1fef1a5227d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea16329af38b01e45d8c60fa21b2753", "guid": "bfdfe7dc352907fc980b868725387e989938fa506bda9838a90e7584d87c1603"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987342ead266053d7cfe62f370101af059", "guid": "bfdfe7dc352907fc980b868725387e980c1cbdf8e54c8d21bb57fdb07ac599f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f36ee075be17537feaccbba3c4b48591", "guid": "bfdfe7dc352907fc980b868725387e98bf8e9e7c7fa715b895a22cab7cbdad49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fef013791f30e0bb669bec613da2795d", "guid": "bfdfe7dc352907fc980b868725387e988ff7f66e8a57ee23bebe7d5ddcde047e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b1e818bf9c57936293102b7889b6728", "guid": "bfdfe7dc352907fc980b868725387e9818c84981e5d0ed50481b410f4edf58b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f15dafd6583ca9ebc5957df4603fb2", "guid": "bfdfe7dc352907fc980b868725387e981f48e1a82db6917f3d8f421ba80a1919"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e1fdef953143c8c457054039f9f9e2", "guid": "bfdfe7dc352907fc980b868725387e984e8193b6770c9e699cfc846814ac6acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b647d4cbbdf70e36c6ab4bb7346eb80", "guid": "bfdfe7dc352907fc980b868725387e98e41f462f4917a0d1ee77b640f1d02368"}], "guid": "bfdfe7dc352907fc980b868725387e98a1a8d88ea1e3388c12dfdd7fc92b7070", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984779792658a77cd2531e1dc5b6ee4baa", "guid": "bfdfe7dc352907fc980b868725387e984b1758cdbb9931c89024e1be91022914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475a134910b5f1d1511facf92383244f", "guid": "bfdfe7dc352907fc980b868725387e9823d6e1bf380b927d79bf8fa030efdde0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836bab5a1ce2824e4b5204d198c195f4a", "guid": "bfdfe7dc352907fc980b868725387e9840211355ba6dbe5dccc3979a2c9f7239"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853c55ba6ef898bfea49d53c84d93d58d", "guid": "bfdfe7dc352907fc980b868725387e9860c30ebfd1d3c06c0d7bc5a72432663b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e96fb517fe2ba1c5131cc060f633b1e", "guid": "bfdfe7dc352907fc980b868725387e9870749788fd4f92592cba13a97e7a0740"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9e3503a66719a7cb57a8bb321afae90", "guid": "bfdfe7dc352907fc980b868725387e987a05c09802a623f6f3d47cf1cf933e47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449ab16aef5f356c2898b72ea72b1874", "guid": "bfdfe7dc352907fc980b868725387e98361d4dad066faac9b73a397df15866b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd6e54bc17822720bdb533668c80a33a", "guid": "bfdfe7dc352907fc980b868725387e98cd07dac0d1b5448bfd33edcb91e83e17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b3f37f9d71fee583cdbf287b81d82a7", "guid": "bfdfe7dc352907fc980b868725387e98988d42c9495f1ae785611e42b360ec8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a8fd24f75912535034806fc092e64b5", "guid": "bfdfe7dc352907fc980b868725387e985020cc3cf1ad4eabbcee52de62357100"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbaccfc57198d69f6708448fd1d48289", "guid": "bfdfe7dc352907fc980b868725387e9817b26eb95bbb6dc83c32b1e85e60c433"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883193d564bd602e1db5ffc0cc0bd4b23", "guid": "bfdfe7dc352907fc980b868725387e984fd9adb6d572390b57c4a08d41b54648"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f42e17f4ed6612a066edfccbdc07f12", "guid": "bfdfe7dc352907fc980b868725387e988c8ce790361b6da6fdf9edc290dbf9df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b2ce22624b6572dd299e072a3e94f95", "guid": "bfdfe7dc352907fc980b868725387e981a8a7df310255ac920bb00878ed86219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98105da323d7013123ecb286baff1cc8b1", "guid": "bfdfe7dc352907fc980b868725387e98ce425a9bed75fc1f3ae1d335fbeddf4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824f2e5c64afaad6d1616bd0dce7e179c", "guid": "bfdfe7dc352907fc980b868725387e988817dc9d81de1852ea4cba4cc6b2418f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a03a6aa3aff83d3d472ccdb8fb3a47b", "guid": "bfdfe7dc352907fc980b868725387e989d99a90956e421d59ba630e7469db178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ff4929a6cefa0efb01a7225ff50a8c", "guid": "bfdfe7dc352907fc980b868725387e985ece4a43d25fd7c573dbd156c4c95472"}], "guid": "bfdfe7dc352907fc980b868725387e98221eb08cdce0d0e12c58d20c34b7caed", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e980400a1196bc179413a482317d53cfb45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e98dfb38cd8c89f96b1cb1dadb54d4cc673"}], "guid": "bfdfe7dc352907fc980b868725387e982898924dc7c5cf76cc16273e39a628e0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e7382a5b445b4e880c98fcb1f5ec764d", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9899bd405c09fa263189632b5ebf1f02f9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bbba59bd09867a1f41274003a836250", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98583eebb8b65047b1e1db5d48cc69d588", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a2b5f5fc3e9ff28be478bf0bf7e6214", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e59e6cf9e135d2976c828dc82ad1b0ae", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a2b5f5fc3e9ff28be478bf0bf7e6214", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ebe46b43f73a98d9ede1a6d1c0f4fa7d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823fd2921567cf05478f8b49b1096208a", "guid": "bfdfe7dc352907fc980b868725387e988429ff2ccd7929b9c5216feeac4b7d19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0c6c9931bd5716a3a38331ac80f0450", "guid": "bfdfe7dc352907fc980b868725387e98c3138bd73e7702dc5c16e09f30d02c5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7cad490aa8f752e5b516f5ae82b139a", "guid": "bfdfe7dc352907fc980b868725387e98274722043f68c383f599575665f7be38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aaed020946abc93f54c79fe55bf9c10", "guid": "bfdfe7dc352907fc980b868725387e98aa0629a458ba33c29f55d6c6fb87eb19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984086c4b46bb81074a4b57933306a15f5", "guid": "bfdfe7dc352907fc980b868725387e9896e3a10b7443eae0a9a710f72bddc784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d84555f855211ff3a30802ce7f2cc3de", "guid": "bfdfe7dc352907fc980b868725387e98b0c16fbf668c46238314d77e089c3c4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc71054cd0908f210b4978d441bd906", "guid": "bfdfe7dc352907fc980b868725387e98623f5925175cc3ebc5a6c860bd3b0977"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc5e13fde0a3476da9960a4874161a8a", "guid": "bfdfe7dc352907fc980b868725387e98f6afe964ed617f0e661da8ff20d71287"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cb6fee1525ff81bc690523f54d36cba", "guid": "bfdfe7dc352907fc980b868725387e98f6841d520c535bb7952fccc04ec133b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e04dc9740cd74e56445976d7a745ae6", "guid": "bfdfe7dc352907fc980b868725387e98878fd09bd613ec3ba846590d92183341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f27e8d5d5a36ecb54d4b3f57c13d0dc1", "guid": "bfdfe7dc352907fc980b868725387e988cf01f25067b7c48aef71fe48e021c50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98012b612aea4e2ab870ac7a722d13e2a8", "guid": "bfdfe7dc352907fc980b868725387e98de1fd72d0d062deabae8884a94fb91dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e504967b1fef2136c80e19909b21e236", "guid": "bfdfe7dc352907fc980b868725387e98fc3919e02b25b49bc7432bdac723ea19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98130c0cea2852d30c6098232983e099e7", "guid": "bfdfe7dc352907fc980b868725387e98ede26b8de9067e91b454866c135118fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849a965d1a9e5f6f07d201a527afb1942", "guid": "bfdfe7dc352907fc980b868725387e98431bb90d3083d28dcdc42cb8fc621b35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f91d20cad8a41a5b99c5a9a83ce2bb8", "guid": "bfdfe7dc352907fc980b868725387e98291c5794b59f183eaba180443fab1b74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0bf2136623fab66361ebe919d31d4b3", "guid": "bfdfe7dc352907fc980b868725387e98e6863fd2c024c8efdaa6a0a9cfc545f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e69ce6c226a4701465f0273857d35478", "guid": "bfdfe7dc352907fc980b868725387e986b32e34a524ca342743825e8fb0201a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891bc29161d4280c37b7fa75867f062c3", "guid": "bfdfe7dc352907fc980b868725387e98909f3d30de6a365229a76d2cb77a7ba1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884880222c67cf041b0d229d3e6052486", "guid": "bfdfe7dc352907fc980b868725387e9873b3453266ca7582a867d9ff672fa409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800eaa3ac1ff7d9c6369aa92252ce3908", "guid": "bfdfe7dc352907fc980b868725387e98d33648127ff116de93be36fa82992cfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893215396acfe45c6e5279f88e8e608ab", "guid": "bfdfe7dc352907fc980b868725387e98b48c486a356048fd0299376881e2871a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98961e55b472e724ffb0534f0b06be0b9e", "guid": "bfdfe7dc352907fc980b868725387e985fa38e447338c463546adb6426242507", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b6a91e3af3be84db2b61a0a4dc69fa01", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c007a16db41cc5c2d90425d53c6436f1", "guid": "bfdfe7dc352907fc980b868725387e9867fddca1ee0797edee53c7fe9429ca56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891459a13e772a576c811a7349a7a842e", "guid": "bfdfe7dc352907fc980b868725387e9844002fd9a211affbdc78389a79eedea1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a711d4a740fabaf439332aa7262b380c", "guid": "bfdfe7dc352907fc980b868725387e98a9cef6e600e03837ccd9f24d35374c47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac76ef5b246101f08e994246459c46e", "guid": "bfdfe7dc352907fc980b868725387e98fc5dd4f3d8f18458f947b8d783653149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c90077601c58cf5f0f7a45b9fefde77a", "guid": "bfdfe7dc352907fc980b868725387e9818d825b5b143f2b71137bb30c8dff3f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda63fd9436ae6181a6e4262378c2d67", "guid": "bfdfe7dc352907fc980b868725387e982ac1e80247f9f8255b6c0b2843e6d470"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e01ad6d01a54a960a9fa6b8a99792298", "guid": "bfdfe7dc352907fc980b868725387e982f22dcbb4d30221a26b2c8771386835c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd08c585111556052e2b9e37d2df91e", "guid": "bfdfe7dc352907fc980b868725387e982cd6fa35505ce91b4e690f3fc2178195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ea22f85ed6865466fc65a4c8286109", "guid": "bfdfe7dc352907fc980b868725387e98202f644701d1e7ed8463e37dd942586e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec4ae555162ff346c67edb5cd1ee302", "guid": "bfdfe7dc352907fc980b868725387e9894b90f8477f312e87ab879e67bf4265e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c9d2dff78bc1eed0075b7c656fd1c60", "guid": "bfdfe7dc352907fc980b868725387e9864e1e6b0132b22eb6d567125a0967ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fefcbfe93fa51304365522e4007bf817", "guid": "bfdfe7dc352907fc980b868725387e98d33eb60c7a5a67d9fd70d93a45bc6f50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebcfa94128f072a50881e88231b19610", "guid": "bfdfe7dc352907fc980b868725387e98e0425c3682827495fe994c26798e3f16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d486c3c45061d50cdbbb38938de910b8", "guid": "bfdfe7dc352907fc980b868725387e9819d8c5be35de488c23e1f11a9224e59f"}], "guid": "bfdfe7dc352907fc980b868725387e9866bf212c9b14914263b424fe0080f77e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98bb93470dbcf5704dd161a13e8f3a88ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec82dece765d451054cf9546cb2f0d4", "guid": "bfdfe7dc352907fc980b868725387e984e58d7b1512fd11fd8913518897f1085"}], "guid": "bfdfe7dc352907fc980b868725387e983bd22f1688314a66fded991fc3c424ed", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98613f9100adaaf1b93fc5e6288b52c884", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98318e70d9f6bf53c53735cfce501032a6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
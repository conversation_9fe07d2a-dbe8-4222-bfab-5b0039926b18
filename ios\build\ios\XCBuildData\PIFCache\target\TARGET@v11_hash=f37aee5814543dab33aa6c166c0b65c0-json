{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98272766e1dea883531f7ae2ccfe5f4259", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9829c180a4d6ad82ab4e2c04d44e749af2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d51cee018ec936c89d5aa817ae10cc66", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98915cc65dc826c5cb0aaf30ed90857bb8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d51cee018ec936c89d5aa817ae10cc66", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c648789cb81f535f66e52b3f4eb8972", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d114f5bb15e1924e90e7bd543334dd12", "guid": "bfdfe7dc352907fc980b868725387e9811ebe7a77ae9896637bc3c7cd7261d80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2654e6361f4b4939e81db7939a5ef6", "guid": "bfdfe7dc352907fc980b868725387e98d2d3630efd9ae22cbdcc5720b5cf04e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989afe94546b643c7f0ed50619878d4692", "guid": "bfdfe7dc352907fc980b868725387e98baa3ea0b7389857e3358a3cc9e508f37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8208e798f6ffd192e2225af861303a", "guid": "bfdfe7dc352907fc980b868725387e98063b0771fee20130f82add6275d83611", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892e86262ee38bee1a54b3f02a29a4876", "guid": "bfdfe7dc352907fc980b868725387e98c5effb5c6bfc19ab44186a370adba6f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e38522828a614546ef57430db8a6d1", "guid": "bfdfe7dc352907fc980b868725387e98e4fcb153feee790f41ca053eda7c8753", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d37a62653f18bfcc0ad5c4a3f9e20ae", "guid": "bfdfe7dc352907fc980b868725387e98a99cfae8e9285fa9a800a67640e5a9cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51c34325bef45b0743ec5c8307dd108", "guid": "bfdfe7dc352907fc980b868725387e98d19a429a301cc30a3ff4ef5aaf688f48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870a2e4b32ce09a5bd446e6e740c0b0be", "guid": "bfdfe7dc352907fc980b868725387e987355c0ffb8e264a29256a34be7b60657", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9843256c6e920e94f5801be22c9f2a43c2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8467916e157c8c8bd89f2d0b5423a64", "guid": "bfdfe7dc352907fc980b868725387e98ea05d5c7789a6ad07f71e92bc665e8f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed09c653d3951b37ba878b0c31b6f0e", "guid": "bfdfe7dc352907fc980b868725387e9868320f2a758f5286ed37eb222a0480a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f125b97f13f995ba406d5b75d811e328", "guid": "bfdfe7dc352907fc980b868725387e98daa0da0eb380f7270a8bec0af9b82cea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff344da9cd3ef02e813c53edd78bcfae", "guid": "bfdfe7dc352907fc980b868725387e988c3a3f7342af78468b6a89dccf0b03bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be80d3524d2f07b31a80d3460797dba3", "guid": "bfdfe7dc352907fc980b868725387e98df855f2210cbb3d9ec4e19ff07445a32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f26c967f941a37ac76f2d5aaba137d", "guid": "bfdfe7dc352907fc980b868725387e98bccbeed0a8cdbc260294d1da829463ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ba772bfcbd524c5dd5205af33227cf9", "guid": "bfdfe7dc352907fc980b868725387e987ea3420c3bc537f87405704b2a96413b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897d2acb3d8e2ba9f938910e0e728fdfc", "guid": "bfdfe7dc352907fc980b868725387e986be75252d49ba3bbea852632b36ce768"}], "guid": "bfdfe7dc352907fc980b868725387e98fa7d3c38cc8d8806e1dc0a84be0506c9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98073a2d61273f7d3e13f4022c98d279c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e9819887f5965f7dce700a8ce3588a4337b"}], "guid": "bfdfe7dc352907fc980b868725387e98683479095ceaff6307aa49bea2f36c53", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986803be10526da38e5ea7932f10b9d8df", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e980f22319b8e3cce37759af36abd595cf1", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e985604724dc6c391b1293ddf7869b9511f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
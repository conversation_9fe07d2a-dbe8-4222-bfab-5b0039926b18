import 'package:flutter/material.dart';

class CouponCustomPainterWidget extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {

    Path path_0 = Path();
    path_0.moveTo(size.width*0.9339771,size.height*0.1360544);
    path_0.cubicTo(size.width*0.9218771,size.height*0.1360544,size.width*0.9089886,size.height*0.1643626,size.width*0.8969600,size.height*0.1674844);
    path_0.cubicTo(size.width*0.8961286,size.height*0.1677000,size.width*0.8952771,size.height*0.1678116,size.width*0.8944143,size.height*0.1678116);
    path_0.cubicTo(size.width*0.8912171,size.height*0.1678116,size.width*0.8882200,size.height*0.1662857,size.width*0.8856343,size.height*0.1636156);
    path_0.cubicTo(size.width*0.8778086,size.height*0.1555333,size.width*0.8698457,size.height*0.1360544,size.width*0.8613143,size.height*0.1360544);
    path_0.cubicTo(size.width*0.8527857,size.height*0.1360544,size.width*0.8448229,size.height*0.1555333,size.width*0.8369971,size.height*0.1636156);
    path_0.cubicTo(size.width*0.8344114,size.height*0.1662857,size.width*0.8314143,size.height*0.1678116,size.width*0.8282171,size.height*0.1678116);
    path_0.cubicTo(size.width*0.8250200,size.height*0.1678116,size.width*0.8220229,size.height*0.1662857,size.width*0.8194371,size.height*0.1636156);
    path_0.cubicTo(size.width*0.8116114,size.height*0.1555340,size.width*0.8036486,size.height*0.1360544,size.width*0.7951200,size.height*0.1360544);
    path_0.cubicTo(size.width*0.7865886,size.height*0.1360544,size.width*0.7786257,size.height*0.1555340,size.width*0.7708000,size.height*0.1636156);
    path_0.cubicTo(size.width*0.7682143,size.height*0.1662857,size.width*0.7652171,size.height*0.1678116,size.width*0.7620200,size.height*0.1678116);
    path_0.cubicTo(size.width*0.7588229,size.height*0.1678116,size.width*0.7558257,size.height*0.1662857,size.width*0.7532429,size.height*0.1636156);
    path_0.cubicTo(size.width*0.7454143,size.height*0.1555333,size.width*0.7374514,size.height*0.1360544,size.width*0.7289229,size.height*0.1360544);
    path_0.cubicTo(size.width*0.7203914,size.height*0.1360544,size.width*0.7124286,size.height*0.1555333,size.width*0.7046029,size.height*0.1636156);
    path_0.cubicTo(size.width*0.7020171,size.height*0.1662857,size.width*0.6990200,size.height*0.1678116,size.width*0.6958229,size.height*0.1678116);
    path_0.cubicTo(size.width*0.6926257,size.height*0.1678116,size.width*0.6896286,size.height*0.1662857,size.width*0.6870457,size.height*0.1636156);
    path_0.cubicTo(size.width*0.6792171,size.height*0.1555333,size.width*0.6712543,size.height*0.1360544,size.width*0.6627257,size.height*0.1360544);
    path_0.cubicTo(size.width*0.6541943,size.height*0.1360544,size.width*0.6462314,size.height*0.1555333,size.width*0.6384057,size.height*0.1636156);
    path_0.cubicTo(size.width*0.6358200,size.height*0.1662857,size.width*0.6328229,size.height*0.1678116,size.width*0.6296257,size.height*0.1678116);
    path_0.cubicTo(size.width*0.6264314,size.height*0.1678116,size.width*0.6234343,size.height*0.1662857,size.width*0.6208486,size.height*0.1636156);
    path_0.cubicTo(size.width*0.6130229,size.height*0.1555333,size.width*0.6050600,size.height*0.1360544,size.width*0.5965286,size.height*0.1360544);
    path_0.cubicTo(size.width*0.5879971,size.height*0.1360544,size.width*0.5800343,size.height*0.1555333,size.width*0.5722086,size.height*0.1636156);
    path_0.cubicTo(size.width*0.5696229,size.height*0.1662857,size.width*0.5666257,size.height*0.1678116,size.width*0.5634286,size.height*0.1678116);
    path_0.cubicTo(size.width*0.5602343,size.height*0.1678116,size.width*0.5572371,size.height*0.1662857,size.width*0.5546514,size.height*0.1636156);
    path_0.cubicTo(size.width*0.5468257,size.height*0.1555333,size.width*0.5388629,size.height*0.1360544,size.width*0.5303314,size.height*0.1360544);
    path_0.cubicTo(size.width*0.5218000,size.height*0.1360544,size.width*0.5138371,size.height*0.1555333,size.width*0.5060114,size.height*0.1636156);
    path_0.cubicTo(size.width*0.5034257,size.height*0.1662857,size.width*0.5004286,size.height*0.1678116,size.width*0.4972314,size.height*0.1678116);
    path_0.cubicTo(size.width*0.4940371,size.height*0.1678116,size.width*0.4910400,size.height*0.1662857,size.width*0.4884543,size.height*0.1636156);
    path_0.cubicTo(size.width*0.4806286,size.height*0.1555333,size.width*0.4726657,size.height*0.1360544,size.width*0.4641343,size.height*0.1360544);
    path_0.cubicTo(size.width*0.4556029,size.height*0.1360544,size.width*0.4476400,size.height*0.1555333,size.width*0.4398143,size.height*0.1636156);
    path_0.cubicTo(size.width*0.4372286,size.height*0.1662857,size.width*0.4342314,size.height*0.1678116,size.width*0.4310371,size.height*0.1678116);
    path_0.cubicTo(size.width*0.4278400,size.height*0.1678116,size.width*0.4248429,size.height*0.1662857,size.width*0.4222571,size.height*0.1636156);
    path_0.cubicTo(size.width*0.4144314,size.height*0.1555333,size.width*0.4064686,size.height*0.1360544,size.width*0.3979371,size.height*0.1360544);
    path_0.cubicTo(size.width*0.3894057,size.height*0.1360544,size.width*0.3814429,size.height*0.1555333,size.width*0.3736171,size.height*0.1636156);
    path_0.cubicTo(size.width*0.3710314,size.height*0.1662857,size.width*0.3680343,size.height*0.1678116,size.width*0.3648400,size.height*0.1678116);
    path_0.cubicTo(size.width*0.3616429,size.height*0.1678116,size.width*0.3586457,size.height*0.1662857,size.width*0.3560600,size.height*0.1636156);
    path_0.cubicTo(size.width*0.3482343,size.height*0.1555340,size.width*0.3402714,size.height*0.1360544,size.width*0.3317400,size.height*0.1360544);
    path_0.cubicTo(size.width*0.3232086,size.height*0.1360544,size.width*0.3152457,size.height*0.1555340,size.width*0.3074200,size.height*0.1636156);
    path_0.cubicTo(size.width*0.3048343,size.height*0.1662857,size.width*0.3018371,size.height*0.1678116,size.width*0.2986429,size.height*0.1678116);
    path_0.cubicTo(size.width*0.2954457,size.height*0.1678116,size.width*0.2924486,size.height*0.1662857,size.width*0.2898629,size.height*0.1636156);
    path_0.cubicTo(size.width*0.2820371,size.height*0.1555333,size.width*0.2740740,size.height*0.1360544,size.width*0.2655434,size.height*0.1360544);
    path_0.cubicTo(size.width*0.2570129,size.height*0.1360544,size.width*0.2490500,size.height*0.1555333,size.width*0.2412240,size.height*0.1636156);
    path_0.cubicTo(size.width*0.2386386,size.height*0.1662857,size.width*0.2356414,size.height*0.1678116,size.width*0.2324451,size.height*0.1678116);
    path_0.cubicTo(size.width*0.2292489,size.height*0.1678116,size.width*0.2262517,size.height*0.1662857,size.width*0.2236663,size.height*0.1636156);
    path_0.cubicTo(size.width*0.2158400,size.height*0.1555333,size.width*0.2078771,size.height*0.1360544,size.width*0.1993466,size.height*0.1360544);
    path_0.cubicTo(size.width*0.1908160,size.height*0.1360544,size.width*0.1828531,size.height*0.1555333,size.width*0.1750271,size.height*0.1636156);
    path_0.cubicTo(size.width*0.1724417,size.height*0.1662857,size.width*0.1694446,size.height*0.1678116,size.width*0.1662483,size.height*0.1678116);
    path_0.cubicTo(size.width*0.1630520,size.height*0.1678116,size.width*0.1600546,size.height*0.1662857,size.width*0.1574694,size.height*0.1636156);
    path_0.cubicTo(size.width*0.1496431,size.height*0.1555333,size.width*0.1416803,size.height*0.1360544,size.width*0.1331497,size.height*0.1360544);
    path_0.cubicTo(size.width*0.1246191,size.height*0.1360544,size.width*0.1166563,size.height*0.1555333,size.width*0.1088300,size.height*0.1636156);
    path_0.cubicTo(size.width*0.1062449,size.height*0.1662857,size.width*0.1032477,size.height*0.1678116,size.width*0.1000511,size.height*0.1678116);
    path_0.cubicTo(size.width*0.09843829,size.height*0.1678116,size.width*0.09687600,size.height*0.1674231,size.width*0.09539200,size.height*0.1666952);
    path_0.cubicTo(size.width*0.08469743,size.height*0.1614483,size.width*0.07354714,size.height*0.1360544,size.width*0.06262800,size.height*0.1360544);
    path_0.cubicTo(size.width*0.05170886,size.height*0.1360544,size.width*0.04285714,size.height*0.1571299,size.width*0.04285714,size.height*0.1831279);
    path_0.lineTo(size.width*0.04285714,size.height*0.8886735);
    path_0.cubicTo(size.width*0.04285714,size.height*0.9125850,size.width*0.05099971,size.height*0.9319728,size.width*0.06104400,size.height*0.9319728);
    path_0.lineTo(size.width*0.08164771,size.height*0.9319728);
    path_0.cubicTo(size.width*0.08208714,size.height*0.9319728,size.width*0.08239886,size.height*0.9310340,size.width*0.08239886,size.height*0.9299864);
    path_0.cubicTo(size.width*0.08239886,size.height*0.9124422,size.width*0.09029829,size.height*0.8982313,size.width*0.1000511,size.height*0.8982313);
    path_0.cubicTo(size.width*0.1098043,size.height*0.8982313,size.width*0.1177037,size.height*0.9124422,size.width*0.1177037,size.height*0.9299864);
    path_0.cubicTo(size.width*0.1177037,size.height*0.9310884,size.width*0.1179911,size.height*0.9319728,size.width*0.1184549,size.height*0.9319728);
    path_0.lineTo(size.width*0.1478889,size.height*0.9319728);
    path_0.cubicTo(size.width*0.1483283,size.height*0.9319728,size.width*0.1486397,size.height*0.9310340,size.width*0.1486397,size.height*0.9299864);
    path_0.cubicTo(size.width*0.1486397,size.height*0.9124422,size.width*0.1565394,size.height*0.8982313,size.width*0.1662923,size.height*0.8982313);
    path_0.cubicTo(size.width*0.1760454,size.height*0.8982313,size.width*0.1839449,size.height*0.9124422,size.width*0.1839449,size.height*0.9299864);
    path_0.cubicTo(size.width*0.1839449,size.height*0.9310884,size.width*0.1842323,size.height*0.9319728,size.width*0.1846957,size.height*0.9319728);
    path_0.lineTo(size.width*0.2141297,size.height*0.9319728);
    path_0.cubicTo(size.width*0.2145694,size.height*0.9319728,size.width*0.2148809,size.height*0.9310340,size.width*0.2148809,size.height*0.9299864);
    path_0.cubicTo(size.width*0.2148809,size.height*0.9124422,size.width*0.2227803,size.height*0.8982313,size.width*0.2325334,size.height*0.8982313);
    path_0.cubicTo(size.width*0.2422863,size.height*0.8982313,size.width*0.2501857,size.height*0.9124422,size.width*0.2501857,size.height*0.9299864);
    path_0.cubicTo(size.width*0.2501857,size.height*0.9310884,size.width*0.2504734,size.height*0.9319728,size.width*0.2509369,size.height*0.9319728);
    path_0.lineTo(size.width*0.2803709,size.height*0.9319728);
    path_0.cubicTo(size.width*0.2808103,size.height*0.9319728,size.width*0.2811220,size.height*0.9310340,size.width*0.2811220,size.height*0.9299864);
    path_0.cubicTo(size.width*0.2811220,size.height*0.9124422,size.width*0.2890200,size.height*0.8982313,size.width*0.2987743,size.height*0.8982313);
    path_0.cubicTo(size.width*0.3085286,size.height*0.8982313,size.width*0.3164257,size.height*0.9124422,size.width*0.3164257,size.height*0.9299864);
    path_0.cubicTo(size.width*0.3164257,size.height*0.9310884,size.width*0.3167143,size.height*0.9319728,size.width*0.3171771,size.height*0.9319728);
    path_0.lineTo(size.width*0.3466114,size.height*0.9319728);
    path_0.cubicTo(size.width*0.3470514,size.height*0.9319728,size.width*0.3473629,size.height*0.9310340,size.width*0.3473629,size.height*0.9299864);
    path_0.cubicTo(size.width*0.3473629,size.height*0.9124422,size.width*0.3552629,size.height*0.8982313,size.width*0.3650143,size.height*0.8982313);
    path_0.cubicTo(size.width*0.3747686,size.height*0.8982313,size.width*0.3826686,size.height*0.9124422,size.width*0.3826686,size.height*0.9299864);
    path_0.cubicTo(size.width*0.3826686,size.height*0.9310884,size.width*0.3829543,size.height*0.9319728,size.width*0.3834200,size.height*0.9319728);
    path_0.lineTo(size.width*0.4128543,size.height*0.9319728);
    path_0.cubicTo(size.width*0.4132914,size.height*0.9319728,size.width*0.4136029,size.height*0.9310340,size.width*0.4136029,size.height*0.9299864);
    path_0.cubicTo(size.width*0.4136029,size.height*0.9124422,size.width*0.4215029,size.height*0.8982313,size.width*0.4312571,size.height*0.8982313);
    path_0.cubicTo(size.width*0.4410086,size.height*0.8982313,size.width*0.4489086,size.height*0.9124422,size.width*0.4489086,size.height*0.9299864);
    path_0.cubicTo(size.width*0.4489086,size.height*0.9310884,size.width*0.4491971,size.height*0.9319728,size.width*0.4496600,size.height*0.9319728);
    path_0.lineTo(size.width*0.4790943,size.height*0.9319728);
    path_0.cubicTo(size.width*0.4795343,size.height*0.9319728,size.width*0.4798457,size.height*0.9310340,size.width*0.4798457,size.height*0.9299864);
    path_0.cubicTo(size.width*0.4798457,size.height*0.9124422,size.width*0.4877457,size.height*0.8982313,size.width*0.4974971,size.height*0.8982313);
    path_0.cubicTo(size.width*0.5072514,size.height*0.8982313,size.width*0.5151514,size.height*0.9124422,size.width*0.5151514,size.height*0.9299864);
    path_0.cubicTo(size.width*0.5151514,size.height*0.9310884,size.width*0.5154371,size.height*0.9319728,size.width*0.5159000,size.height*0.9319728);
    path_0.lineTo(size.width*0.5453343,size.height*0.9319728);
    path_0.cubicTo(size.width*0.5457743,size.height*0.9319728,size.width*0.5460857,size.height*0.9310340,size.width*0.5460857,size.height*0.9299864);
    path_0.cubicTo(size.width*0.5460857,size.height*0.9124422,size.width*0.5539857,size.height*0.8982313,size.width*0.5637371,size.height*0.8982313);
    path_0.cubicTo(size.width*0.5734914,size.height*0.8982313,size.width*0.5813914,size.height*0.9124422,size.width*0.5813914,size.height*0.9299864);
    path_0.cubicTo(size.width*0.5813914,size.height*0.9310884,size.width*0.5816771,size.height*0.9319728,size.width*0.5821429,size.height*0.9319728);
    path_0.lineTo(size.width*0.6115771,size.height*0.9319728);
    path_0.cubicTo(size.width*0.6120143,size.height*0.9319728,size.width*0.6123257,size.height*0.9310340,size.width*0.6123257,size.height*0.9299864);
    path_0.cubicTo(size.width*0.6123257,size.height*0.9124422,size.width*0.6202257,size.height*0.8982313,size.width*0.6299800,size.height*0.8982313);
    path_0.cubicTo(size.width*0.6397314,size.height*0.8982313,size.width*0.6476314,size.height*0.9124422,size.width*0.6476314,size.height*0.9299864);
    path_0.cubicTo(size.width*0.6476314,size.height*0.9310884,size.width*0.6479200,size.height*0.9319728,size.width*0.6483829,size.height*0.9319728);
    path_0.lineTo(size.width*0.6778171,size.height*0.9319728);
    path_0.cubicTo(size.width*0.6782571,size.height*0.9319728,size.width*0.6785686,size.height*0.9310340,size.width*0.6785686,size.height*0.9299864);
    path_0.cubicTo(size.width*0.6785686,size.height*0.9124422,size.width*0.6864686,size.height*0.8982313,size.width*0.6962200,size.height*0.8982313);
    path_0.cubicTo(size.width*0.7059743,size.height*0.8982313,size.width*0.7138743,size.height*0.9124422,size.width*0.7138743,size.height*0.9299864);
    path_0.cubicTo(size.width*0.7138743,size.height*0.9310884,size.width*0.7141600,size.height*0.9319728,size.width*0.7146229,size.height*0.9319728);
    path_0.lineTo(size.width*0.7440571,size.height*0.9319728);
    path_0.cubicTo(size.width*0.7444971,size.height*0.9319728,size.width*0.7448086,size.height*0.9310340,size.width*0.7448086,size.height*0.9299864);
    path_0.cubicTo(size.width*0.7448086,size.height*0.9124422,size.width*0.7527086,size.height*0.8982313,size.width*0.7624629,size.height*0.8982313);
    path_0.cubicTo(size.width*0.7722143,size.height*0.8982313,size.width*0.7801143,size.height*0.9124422,size.width*0.7801143,size.height*0.9299864);
    path_0.cubicTo(size.width*0.7801143,size.height*0.9310884,size.width*0.7804029,size.height*0.9319728,size.width*0.7808657,size.height*0.9319728);
    path_0.lineTo(size.width*0.8103000,size.height*0.9319728);
    path_0.cubicTo(size.width*0.8107400,size.height*0.9319728,size.width*0.8110514,size.height*0.9310340,size.width*0.8110514,size.height*0.9299864);
    path_0.cubicTo(size.width*0.8110514,size.height*0.9124422,size.width*0.8189486,size.height*0.8982313,size.width*0.8287029,size.height*0.8982313);
    path_0.cubicTo(size.width*0.8384543,size.height*0.8982313,size.width*0.8463543,size.height*0.9124422,size.width*0.8463543,size.height*0.9299864);
    path_0.cubicTo(size.width*0.8463543,size.height*0.9310884,size.width*0.8466429,size.height*0.9319728,size.width*0.8471057,size.height*0.9319728);
    path_0.lineTo(size.width*0.8765400,size.height*0.9319728);
    path_0.cubicTo(size.width*0.8769800,size.height*0.9319728,size.width*0.8772914,size.height*0.9310340,size.width*0.8772914,size.height*0.9299864);
    path_0.cubicTo(size.width*0.8772914,size.height*0.9124422,size.width*0.8851914,size.height*0.8982313,size.width*0.8949429,size.height*0.8982313);
    path_0.cubicTo(size.width*0.9046971,size.height*0.8982313,size.width*0.9125971,size.height*0.9124422,size.width*0.9125971,size.height*0.9299864);
    path_0.cubicTo(size.width*0.9125971,size.height*0.9310884,size.width*0.9128829,size.height*0.9319728,size.width*0.9133486,size.height*0.9319728);
    path_0.lineTo(size.width*0.9341971,size.height*0.9319728);
    path_0.cubicTo(size.width*0.9461771,size.height*0.9319728,size.width*0.9558886,size.height*0.9088503,size.width*0.9558886,size.height*0.8803265);
    path_0.lineTo(size.width*0.9558886,size.height*0.1882238);
    path_0.cubicTo(size.width*0.9558886,size.height*0.1594116,size.width*0.9460800,size.height*0.1360544,size.width*0.9339771,size.height*0.1360544);
    path_0.close();

    Paint paint0Fill = Paint()..style=PaintingStyle.fill;
    paint0Fill.color = Colors.white.withValues(alpha: 1.0);
    canvas.drawPath(path_0,paint0Fill);

    Path path_1 = Path();
    path_1.moveTo(size.width*0.04411000,size.height*0.7142857);
    path_1.lineTo(size.width*0.04411000,size.height*0.9319728);
    path_1.lineTo(size.width*0.08373971,size.height*0.9319728);
    path_1.cubicTo(size.width*0.08369571,size.height*0.9314286,size.width*0.08365143,size.height*0.9308912,size.width*0.08365143,size.height*0.9302789);
    path_1.cubicTo(size.width*0.08365143,size.height*0.9153401,size.width*0.09155114,size.height*0.9032313,size.width*0.1013040,size.height*0.9032313);
    path_1.cubicTo(size.width*0.1110571,size.height*0.9032313,size.width*0.1189566,size.height*0.9153401,size.width*0.1189566,size.height*0.9302789);
    path_1.cubicTo(size.width*0.1189566,size.height*0.9308231,size.width*0.1188683,size.height*0.9313673,size.width*0.1188683,size.height*0.9319728);
    path_1.lineTo(size.width*0.1499809,size.height*0.9319728);
    path_1.cubicTo(size.width*0.1499366,size.height*0.9314286,size.width*0.1498926,size.height*0.9308912,size.width*0.1498926,size.height*0.9302789);
    path_1.cubicTo(size.width*0.1498926,size.height*0.9153401,size.width*0.1577920,size.height*0.9032313,size.width*0.1675451,size.height*0.9032313);
    path_1.cubicTo(size.width*0.1772980,size.height*0.9032313,size.width*0.1851977,size.height*0.9153401,size.width*0.1851977,size.height*0.9302789);
    path_1.cubicTo(size.width*0.1851977,size.height*0.9308231,size.width*0.1851094,size.height*0.9313673,size.width*0.1851094,size.height*0.9319728);
    path_1.lineTo(size.width*0.2162220,size.height*0.9319728);
    path_1.cubicTo(size.width*0.2161777,size.height*0.9314286,size.width*0.2161337,size.height*0.9308912,size.width*0.2161337,size.height*0.9302789);
    path_1.cubicTo(size.width*0.2161337,size.height*0.9153401,size.width*0.2240331,size.height*0.9032313,size.width*0.2337860,size.height*0.9032313);
    path_1.cubicTo(size.width*0.2435391,size.height*0.9032313,size.width*0.2514386,size.height*0.9153401,size.width*0.2514386,size.height*0.9302789);
    path_1.cubicTo(size.width*0.2514386,size.height*0.9308231,size.width*0.2513503,size.height*0.9313673,size.width*0.2513503,size.height*0.9319728);
    path_1.lineTo(size.width*0.2824629,size.height*0.9319728);
    path_1.cubicTo(size.width*0.2824189,size.height*0.9314286,size.width*0.2823746,size.height*0.9308912,size.width*0.2823746,size.height*0.9302789);
    path_1.cubicTo(size.width*0.2823746,size.height*0.9153401,size.width*0.2902743,size.height*0.9032313,size.width*0.3000286,size.height*0.9032313);
    path_1.cubicTo(size.width*0.3097800,size.height*0.9032313,size.width*0.3176800,size.height*0.9153401,size.width*0.3176800,size.height*0.9302789);
    path_1.cubicTo(size.width*0.3176800,size.height*0.9308231,size.width*0.3175914,size.height*0.9313673,size.width*0.3175914,size.height*0.9319728);
    path_1.lineTo(size.width*0.3487029,size.height*0.9319728);
    path_1.cubicTo(size.width*0.3486600,size.height*0.9314286,size.width*0.3486143,size.height*0.9308912,size.width*0.3486143,size.height*0.9302789);
    path_1.cubicTo(size.width*0.3486143,size.height*0.9153401,size.width*0.3565143,size.height*0.9032313,size.width*0.3662686,size.height*0.9032313);
    path_1.cubicTo(size.width*0.3760200,size.height*0.9032313,size.width*0.3839200,size.height*0.9153401,size.width*0.3839200,size.height*0.9302789);
    path_1.cubicTo(size.width*0.3839200,size.height*0.9308231,size.width*0.3838314,size.height*0.9313673,size.width*0.3838314,size.height*0.9319728);
    path_1.lineTo(size.width*0.4149457,size.height*0.9319728);
    path_1.cubicTo(size.width*0.4149000,size.height*0.9314286,size.width*0.4148571,size.height*0.9308912,size.width*0.4148571,size.height*0.9302789);
    path_1.cubicTo(size.width*0.4148571,size.height*0.9153401,size.width*0.4227571,size.height*0.9032313,size.width*0.4325086,size.height*0.9032313);
    path_1.cubicTo(size.width*0.4422629,size.height*0.9032313,size.width*0.4501629,size.height*0.9153401,size.width*0.4501629,size.height*0.9302789);
    path_1.cubicTo(size.width*0.4501629,size.height*0.9308231,size.width*0.4500743,size.height*0.9313673,size.width*0.4500743,size.height*0.9319728);
    path_1.lineTo(size.width*0.4811857,size.height*0.9319728);
    path_1.cubicTo(size.width*0.4811429,size.height*0.9314286,size.width*0.4810971,size.height*0.9308912,size.width*0.4810971,size.height*0.9302789);
    path_1.cubicTo(size.width*0.4810971,size.height*0.9153401,size.width*0.4889971,size.height*0.9032313,size.width*0.4987514,size.height*0.9032313);
    path_1.cubicTo(size.width*0.5085029,size.height*0.9032313,size.width*0.5164029,size.height*0.9153401,size.width*0.5164029,size.height*0.9302789);
    path_1.cubicTo(size.width*0.5164029,size.height*0.9308231,size.width*0.5163143,size.height*0.9313673,size.width*0.5163143,size.height*0.9319728);
    path_1.lineTo(size.width*0.5474257,size.height*0.9319728);
    path_1.cubicTo(size.width*0.5473829,size.height*0.9314286,size.width*0.5473400,size.height*0.9308912,size.width*0.5473400,size.height*0.9302789);
    path_1.cubicTo(size.width*0.5473400,size.height*0.9153401,size.width*0.5552371,size.height*0.9032313,size.width*0.5649914,size.height*0.9032313);
    path_1.cubicTo(size.width*0.5747457,size.height*0.9032313,size.width*0.5826429,size.height*0.9153401,size.width*0.5826429,size.height*0.9302789);
    path_1.cubicTo(size.width*0.5826429,size.height*0.9308231,size.width*0.5825543,size.height*0.9313673,size.width*0.5825543,size.height*0.9319728);
    path_1.lineTo(size.width*0.6136686,size.height*0.9319728);
    path_1.cubicTo(size.width*0.6136229,size.height*0.9314286,size.width*0.6135800,size.height*0.9308912,size.width*0.6135800,size.height*0.9302789);
    path_1.cubicTo(size.width*0.6135800,size.height*0.9153401,size.width*0.6214800,size.height*0.9032313,size.width*0.6312314,size.height*0.9032313);
    path_1.cubicTo(size.width*0.6409857,size.height*0.9032313,size.width*0.6488857,size.height*0.9153401,size.width*0.6488857,size.height*0.9302789);
    path_1.cubicTo(size.width*0.6488857,size.height*0.9308231,size.width*0.6487971,size.height*0.9313673,size.width*0.6487971,size.height*0.9319728);
    path_1.lineTo(size.width*0.6799086,size.height*0.9319728);
    path_1.cubicTo(size.width*0.6798657,size.height*0.9314286,size.width*0.6798200,size.height*0.9308912,size.width*0.6798200,size.height*0.9302789);
    path_1.cubicTo(size.width*0.6798200,size.height*0.9153401,size.width*0.6877200,size.height*0.9032313,size.width*0.6974743,size.height*0.9032313);
    path_1.cubicTo(size.width*0.7072257,size.height*0.9032313,size.width*0.7151257,size.height*0.9153401,size.width*0.7151257,size.height*0.9302789);
    path_1.cubicTo(size.width*0.7151257,size.height*0.9308231,size.width*0.7150371,size.height*0.9313673,size.width*0.7150371,size.height*0.9319728);
    path_1.lineTo(size.width*0.7461514,size.height*0.9319728);
    path_1.cubicTo(size.width*0.7461057,size.height*0.9314286,size.width*0.7460629,size.height*0.9308912,size.width*0.7460629,size.height*0.9302789);
    path_1.cubicTo(size.width*0.7460629,size.height*0.9153401,size.width*0.7539600,size.height*0.9032313,size.width*0.7637143,size.height*0.9032313);
    path_1.cubicTo(size.width*0.7734686,size.height*0.9032313,size.width*0.7813657,size.height*0.9153401,size.width*0.7813657,size.height*0.9302789);
    path_1.cubicTo(size.width*0.7813657,size.height*0.9308231,size.width*0.7812800,size.height*0.9313673,size.width*0.7812800,size.height*0.9319728);
    path_1.lineTo(size.width*0.8123914,size.height*0.9319728);
    path_1.cubicTo(size.width*0.8123457,size.height*0.9314286,size.width*0.8123029,size.height*0.9308912,size.width*0.8123029,size.height*0.9302789);
    path_1.cubicTo(size.width*0.8123029,size.height*0.9153401,size.width*0.8202029,size.height*0.9032313,size.width*0.8299543,size.height*0.9032313);
    path_1.cubicTo(size.width*0.8397086,size.height*0.9032313,size.width*0.8476086,size.height*0.9153401,size.width*0.8476086,size.height*0.9302789);
    path_1.cubicTo(size.width*0.8476086,size.height*0.9308231,size.width*0.8475200,size.height*0.9313673,size.width*0.8475200,size.height*0.9319728);
    path_1.lineTo(size.width*0.8786314,size.height*0.9319728);
    path_1.cubicTo(size.width*0.8785886,size.height*0.9314286,size.width*0.8785429,size.height*0.9308912,size.width*0.8785429,size.height*0.9302789);
    path_1.cubicTo(size.width*0.8785429,size.height*0.9153401,size.width*0.8864429,size.height*0.9032313,size.width*0.8961971,size.height*0.9032313);
    path_1.cubicTo(size.width*0.9059486,size.height*0.9032313,size.width*0.9138486,size.height*0.9153401,size.width*0.9138486,size.height*0.9302789);
    path_1.cubicTo(size.width*0.9138486,size.height*0.9308231,size.width*0.9137600,size.height*0.9313673,size.width*0.9137600,size.height*0.9319728);
    path_1.lineTo(size.width*0.9571429,size.height*0.9319728);
    path_1.lineTo(size.width*0.9571429,size.height*0.7142857);
    path_1.lineTo(size.width*0.04411000,size.height*0.7142857);
    path_1.close();

    Paint paint1Fill = Paint()..style=PaintingStyle.fill;
    paint1Fill.color = const Color(0xffEFE6FE).withValues(alpha: 1.0);
    canvas.drawPath(path_1,paint1Fill);

    Path path_2 = Path();
    path_2.moveTo(size.width*0.2227734,size.height*0.4546959);
    path_2.cubicTo(size.width*0.2214591,size.height*0.4546959,size.width*0.2202971,size.height*0.4544007,size.width*0.2192877,size.height*0.4538116);
    path_2.cubicTo(size.width*0.2182971,size.height*0.4532218,size.width*0.2174686,size.height*0.4520878,size.width*0.2168020,size.height*0.4504102);
    path_2.cubicTo(size.width*0.2161543,size.height*0.4486864,size.width*0.2156591,size.height*0.4461469,size.width*0.2153163,size.height*0.4427912);
    path_2.cubicTo(size.width*0.2149923,size.height*0.4394347,size.width*0.2148306,size.height*0.4350361,size.width*0.2148306,size.height*0.4295939);
    path_2.cubicTo(size.width*0.2148306,size.height*0.4241061,size.width*0.2149923,size.height*0.4196844,size.width*0.2153163,size.height*0.4163286);
    path_2.cubicTo(size.width*0.2156591,size.height*0.4129272,size.width*0.2161637,size.height*0.4103422,size.width*0.2168306,size.height*0.4085735);
    path_2.cubicTo(size.width*0.2175163,size.height*0.4067592,size.width*0.2183543,size.height*0.4055347,size.width*0.2193449,size.height*0.4049000);
    path_2.cubicTo(size.width*0.2203351,size.height*0.4042646,size.width*0.2214780,size.height*0.4039476,size.width*0.2227734,size.height*0.4039476);
    path_2.cubicTo(size.width*0.2241257,size.height*0.4039476,size.width*0.2252971,size.height*0.4042646,size.width*0.2262877,size.height*0.4049000);
    path_2.cubicTo(size.width*0.2272971,size.height*0.4055347,size.width*0.2281257,size.height*0.4067592,size.width*0.2287734,size.height*0.4085735);
    path_2.cubicTo(size.width*0.2294400,size.height*0.4103422,size.width*0.2299257,size.height*0.4129272,size.width*0.2302306,size.height*0.4163286);
    path_2.cubicTo(size.width*0.2305543,size.height*0.4196844,size.width*0.2307163,size.height*0.4241061,size.width*0.2307163,size.height*0.4295939);
    path_2.cubicTo(size.width*0.2307163,size.height*0.4350361,size.width*0.2305543,size.height*0.4394347,size.width*0.2302306,size.height*0.4427912);
    path_2.cubicTo(size.width*0.2299066,size.height*0.4461469,size.width*0.2294114,size.height*0.4486864,size.width*0.2287449,size.height*0.4504102);
    path_2.cubicTo(size.width*0.2280780,size.height*0.4520878,size.width*0.2272494,size.height*0.4532218,size.width*0.2262591,size.height*0.4538116);
    path_2.cubicTo(size.width*0.2252686,size.height*0.4544007,size.width*0.2241066,size.height*0.4546959,size.width*0.2227734,size.height*0.4546959);
    path_2.close();
    path_2.moveTo(size.width*0.2227734,size.height*0.4511585);
    path_2.cubicTo(size.width*0.2238971,size.height*0.4511585,size.width*0.2248591,size.height*0.4509088,size.width*0.2256591,size.height*0.4504102);
    path_2.cubicTo(size.width*0.2264591,size.height*0.4498660,size.width*0.2271066,size.height*0.4488456,size.width*0.2276020,size.height*0.4473490);
    path_2.cubicTo(size.width*0.2281163,size.height*0.4458524,size.width*0.2284877,size.height*0.4436980,size.width*0.2287163,size.height*0.4408864);
    path_2.cubicTo(size.width*0.2289449,size.height*0.4380293,size.width*0.2290591,size.height*0.4342646,size.width*0.2290591,size.height*0.4295939);
    path_2.cubicTo(size.width*0.2290591,size.height*0.4246959,size.width*0.2289351,size.height*0.4207728,size.width*0.2286877,size.height*0.4178252);
    path_2.cubicTo(size.width*0.2284591,size.height*0.4148769,size.width*0.2280877,size.height*0.4126776,size.width*0.2275734,size.height*0.4112265);
    path_2.cubicTo(size.width*0.2270780,size.height*0.4097299,size.width*0.2264306,size.height*0.4087320,size.width*0.2256306,size.height*0.4082333);
    path_2.cubicTo(size.width*0.2248494,size.height*0.4077340,size.width*0.2238971,size.height*0.4074850,size.width*0.2227734,size.height*0.4074850);
    path_2.cubicTo(size.width*0.2217449,size.height*0.4074850,size.width*0.2208306,size.height*0.4077340,size.width*0.2200306,size.height*0.4082333);
    path_2.cubicTo(size.width*0.2192494,size.height*0.4087320,size.width*0.2185923,size.height*0.4097299,size.width*0.2180591,size.height*0.4112265);
    path_2.cubicTo(size.width*0.2175449,size.height*0.4126776,size.width*0.2171543,size.height*0.4148769,size.width*0.2168877,size.height*0.4178252);
    path_2.cubicTo(size.width*0.2166209,size.height*0.4207728,size.width*0.2164877,size.height*0.4246959,size.width*0.2164877,size.height*0.4295939);
    path_2.cubicTo(size.width*0.2164877,size.height*0.4342646,size.width*0.2165923,size.height*0.4380293,size.width*0.2168020,size.height*0.4408864);
    path_2.cubicTo(size.width*0.2170306,size.height*0.4436980,size.width*0.2173923,size.height*0.4458524,size.width*0.2178877,size.height*0.4473490);
    path_2.cubicTo(size.width*0.2183829,size.height*0.4488456,size.width*0.2190306,size.height*0.4498660,size.width*0.2198306,size.height*0.4504102);
    path_2.cubicTo(size.width*0.2206306,size.height*0.4509088,size.width*0.2216114,size.height*0.4511585,size.width*0.2227734,size.height*0.4511585);
    path_2.close();
    path_2.moveTo(size.width*0.2348203,size.height*0.4542878);
    path_2.lineTo(size.width*0.2348203,size.height*0.4139476);
    path_2.cubicTo(size.width*0.2348203,size.height*0.4120878,size.width*0.2349346,size.height*0.4105463,size.width*0.2351631,size.height*0.4093218);
    path_2.cubicTo(size.width*0.2353917,size.height*0.4080973,size.width*0.2356963,size.height*0.4071218,size.width*0.2360774,size.height*0.4063966);
    path_2.cubicTo(size.width*0.2364583,size.height*0.4056707,size.width*0.2368774,size.height*0.4051490,size.width*0.2373346,size.height*0.4048320);
    path_2.cubicTo(size.width*0.2378106,size.height*0.4045143,size.width*0.2382869,size.height*0.4043558,size.width*0.2387631,size.height*0.4043558);
    path_2.cubicTo(size.width*0.2396391,size.height*0.4044007,size.width*0.2405631,size.height*0.4044463,size.width*0.2415346,size.height*0.4044918);
    path_2.cubicTo(size.width*0.2425060,size.height*0.4044918,size.width*0.2434869,size.height*0.4045367,size.width*0.2444774,size.height*0.4046279);
    path_2.cubicTo(size.width*0.2454869,size.height*0.4046728,size.width*0.2464677,size.height*0.4047639,size.width*0.2474203,size.height*0.4049000);
    path_2.lineTo(size.width*0.2474203,size.height*0.4079612);
    path_2.lineTo(size.width*0.2390774,size.height*0.4079612);
    path_2.cubicTo(size.width*0.2382963,size.height*0.4079612,size.width*0.2376677,size.height*0.4085054,size.width*0.2371917,size.height*0.4095939);
    path_2.cubicTo(size.width*0.2367154,size.height*0.4106367,size.width*0.2364774,size.height*0.4122469,size.width*0.2364774,size.height*0.4144238);
    path_2.lineTo(size.width*0.2364774,size.height*0.4287095);
    path_2.lineTo(size.width*0.2462203,size.height*0.4290497);
    path_2.lineTo(size.width*0.2462203,size.height*0.4319748);
    path_2.lineTo(size.width*0.2364774,size.height*0.4323150);
    path_2.lineTo(size.width*0.2364774,size.height*0.4542878);
    path_2.lineTo(size.width*0.2348203,size.height*0.4542878);
    path_2.close();
    path_2.moveTo(size.width*0.2506406,size.height*0.4542878);
    path_2.lineTo(size.width*0.2506406,size.height*0.4139476);
    path_2.cubicTo(size.width*0.2506406,size.height*0.4120878,size.width*0.2507549,size.height*0.4105463,size.width*0.2509834,size.height*0.4093218);
    path_2.cubicTo(size.width*0.2512120,size.height*0.4080973,size.width*0.2515169,size.height*0.4071218,size.width*0.2518977,size.height*0.4063966);
    path_2.cubicTo(size.width*0.2522786,size.height*0.4056707,size.width*0.2526977,size.height*0.4051490,size.width*0.2531549,size.height*0.4048320);
    path_2.cubicTo(size.width*0.2536311,size.height*0.4045143,size.width*0.2541071,size.height*0.4043558,size.width*0.2545834,size.height*0.4043558);
    path_2.cubicTo(size.width*0.2554597,size.height*0.4044007,size.width*0.2563834,size.height*0.4044463,size.width*0.2573549,size.height*0.4044918);
    path_2.cubicTo(size.width*0.2583263,size.height*0.4044918,size.width*0.2593071,size.height*0.4045367,size.width*0.2602977,size.height*0.4046279);
    path_2.cubicTo(size.width*0.2613071,size.height*0.4046728,size.width*0.2622883,size.height*0.4047639,size.width*0.2632406,size.height*0.4049000);
    path_2.lineTo(size.width*0.2632406,size.height*0.4079612);
    path_2.lineTo(size.width*0.2548977,size.height*0.4079612);
    path_2.cubicTo(size.width*0.2541169,size.height*0.4079612,size.width*0.2534883,size.height*0.4085054,size.width*0.2530120,size.height*0.4095939);
    path_2.cubicTo(size.width*0.2525357,size.height*0.4106367,size.width*0.2522977,size.height*0.4122469,size.width*0.2522977,size.height*0.4144238);
    path_2.lineTo(size.width*0.2522977,size.height*0.4287095);
    path_2.lineTo(size.width*0.2620406,size.height*0.4290497);
    path_2.lineTo(size.width*0.2620406,size.height*0.4319748);
    path_2.lineTo(size.width*0.2522977,size.height*0.4323150);
    path_2.lineTo(size.width*0.2522977,size.height*0.4542878);
    path_2.lineTo(size.width*0.2506406,size.height*0.4542878);
    path_2.close();

    Paint paint2Fill = Paint()..style=PaintingStyle.fill;
    paint2Fill.color = Colors.white.withValues(alpha: 1.0);
    canvas.drawPath(path_2,paint2Fill);

    Path path_3 = Path();
    path_3.moveTo(size.width*0.1242426,size.height*0.3673741);
    path_3.lineTo(size.width*0.1239854,size.height*0.3578231);
    path_3.lineTo(size.width*0.1280483,size.height*0.3548844);
    path_3.lineTo(size.width*0.1283054,size.height*0.3644354);
    path_3.lineTo(size.width*0.1242426,size.height*0.3673741);
    path_3.close();
    path_3.moveTo(size.width*0.1239854,size.height*0.4666803);
    path_3.lineTo(size.width*0.1239854,size.height*0.4545578);
    path_3.lineTo(size.width*0.1283054,size.height*0.4546803);
    path_3.lineTo(size.width*0.1283054,size.height*0.4666803);
    path_3.lineTo(size.width*0.1239854,size.height*0.4666803);
    path_3.close();
    path_3.moveTo(size.width*0.1260940,size.height*0.4565170);
    path_3.cubicTo(size.width*0.1250997,size.height*0.4565170,size.width*0.1240711,size.height*0.4564354,size.width*0.1230083,size.height*0.4562721);
    path_3.cubicTo(size.width*0.1219797,size.height*0.4561905,size.width*0.1209169,size.height*0.4560272,size.width*0.1198197,size.height*0.4557823);
    path_3.cubicTo(size.width*0.1187569,size.height*0.4554558,size.width*0.1177454,size.height*0.4551293,size.width*0.1167854,size.height*0.4548027);
    path_3.cubicTo(size.width*0.1158254,size.height*0.4543946,size.width*0.1149511,size.height*0.4539456,size.width*0.1141626,size.height*0.4534558);
    path_3.lineTo(size.width*0.1141626,size.height*0.4391293);
    path_3.cubicTo(size.width*0.1151911,size.height*0.4393741,size.width*0.1163569,size.height*0.4395782,size.width*0.1176597,size.height*0.4397415);
    path_3.cubicTo(size.width*0.1189626,size.height*0.4399048,size.width*0.1203169,size.height*0.4400272,size.width*0.1217226,size.height*0.4401088);
    path_3.cubicTo(size.width*0.1231283,size.height*0.4401905,size.width*0.1244654,size.height*0.4402313,size.width*0.1257340,size.height*0.4402313);
    path_3.cubicTo(size.width*0.1270369,size.height*0.4402313,size.width*0.1281511,size.height*0.4399864,size.width*0.1290769,size.height*0.4394966);
    path_3.cubicTo(size.width*0.1300369,size.height*0.4389252,size.width*0.1307740,size.height*0.4379864,size.width*0.1312883,size.height*0.4366803);
    path_3.cubicTo(size.width*0.1318026,size.height*0.4352925,size.width*0.1320597,size.height*0.4334150,size.width*0.1320597,size.height*0.4310476);
    path_3.lineTo(size.width*0.1320597,size.height*0.4277415);
    path_3.cubicTo(size.width*0.1320597,size.height*0.4250476,size.width*0.1317169,size.height*0.4230068,size.width*0.1310311,size.height*0.4216190);
    path_3.cubicTo(size.width*0.1303797,size.height*0.4201497,size.width*0.1294369,size.height*0.4194150,size.width*0.1282026,size.height*0.4194150);
    path_3.lineTo(size.width*0.1249626,size.height*0.4194150);
    path_3.cubicTo(size.width*0.1213283,size.height*0.4194150,size.width*0.1185340,size.height*0.4175374,size.width*0.1165797,size.height*0.4137823);
    path_3.cubicTo(size.width*0.1146597,size.height*0.4099456,size.width*0.1136997,size.height*0.4034558,size.width*0.1136997,size.height*0.3943129);
    path_3.lineTo(size.width*0.1136997,size.height*0.3899048);
    path_3.cubicTo(size.width*0.1136997,size.height*0.3810884,size.width*0.1147626,size.height*0.3746395,size.width*0.1168883,size.height*0.3705578);
    path_3.cubicTo(size.width*0.1190140,size.height*0.3664762,size.width*0.1220483,size.height*0.3644354,size.width*0.1259911,size.height*0.3644354);
    path_3.cubicTo(size.width*0.1273969,size.height*0.3644354,size.width*0.1287854,size.height*0.3645986,size.width*0.1301569,size.height*0.3649252);
    path_3.cubicTo(size.width*0.1315626,size.height*0.3652517,size.width*0.1328997,size.height*0.3656599,size.width*0.1341683,size.height*0.3661497);
    path_3.cubicTo(size.width*0.1354711,size.height*0.3665578,size.width*0.1365854,size.height*0.3670068,size.width*0.1375111,size.height*0.3674966);
    path_3.lineTo(size.width*0.1375111,size.height*0.3818231);
    path_3.cubicTo(size.width*0.1359340,size.height*0.3815782,size.width*0.1341683,size.height*0.3813333,size.width*0.1322140,size.height*0.3810884);
    path_3.cubicTo(size.width*0.1302940,size.height*0.3808435,size.width*0.1285111,size.height*0.3807211,size.width*0.1268654,size.height*0.3807211);
    path_3.cubicTo(size.width*0.1256997,size.height*0.3807211,size.width*0.1246540,size.height*0.3810068,size.width*0.1237283,size.height*0.3815782);
    path_3.cubicTo(size.width*0.1228369,size.height*0.3820680,size.width*0.1221511,size.height*0.3830476,size.width*0.1216711,size.height*0.3845170);
    path_3.cubicTo(size.width*0.1211911,size.height*0.3859864,size.width*0.1209511,size.height*0.3880680,size.width*0.1209511,size.height*0.3907619);
    path_3.lineTo(size.width*0.1209511,size.height*0.3932109);
    path_3.cubicTo(size.width*0.1209511,size.height*0.3963946,size.width*0.1213283,size.height*0.3986803,size.width*0.1220826,size.height*0.4000680);
    path_3.cubicTo(size.width*0.1228369,size.height*0.4014558,size.width*0.1239511,size.height*0.4021497,size.width*0.1254254,size.height*0.4021497);
    path_3.lineTo(size.width*0.1291797,size.height*0.4021497);
    path_3.cubicTo(size.width*0.1314769,size.height*0.4021497,size.width*0.1333626,size.height*0.4031701,size.width*0.1348369,size.height*0.4052109);
    path_3.cubicTo(size.width*0.1363454,size.height*0.4071701,size.width*0.1374597,size.height*0.4099048,size.width*0.1381797,size.height*0.4134150);
    path_3.cubicTo(size.width*0.1389340,size.height*0.4169252,size.width*0.1393111,size.height*0.4209252,size.width*0.1393111,size.height*0.4254150);
    path_3.lineTo(size.width*0.1393111,size.height*0.4305578);
    path_3.cubicTo(size.width*0.1393111,size.height*0.4376599,size.width*0.1387454,size.height*0.4430884,size.width*0.1376140,size.height*0.4468435);
    path_3.cubicTo(size.width*0.1365169,size.height*0.4505986,size.width*0.1349740,size.height*0.4531701,size.width*0.1329854,size.height*0.4545578);
    path_3.cubicTo(size.width*0.1310311,size.height*0.4558639,size.width*0.1287340,size.height*0.4565170,size.width*0.1260940,size.height*0.4565170);
    path_3.close();
    path_3.moveTo(size.width*0.1487420,size.height*0.4557823);
    path_3.lineTo(size.width*0.1600563,size.height*0.3832925);
    path_3.lineTo(size.width*0.1438563,size.height*0.3832925);
    path_3.lineTo(size.width*0.1438563,size.height*0.3651701);
    path_3.lineTo(size.width*0.1678734,size.height*0.3651701);
    path_3.lineTo(size.width*0.1698791,size.height*0.3728844);
    path_3.lineTo(size.width*0.1572791,size.height*0.4557823);
    path_3.lineTo(size.width*0.1487420,size.height*0.4557823);
    path_3.close();
    path_3.moveTo(size.width*0.1872986,size.height*0.4563946);
    path_3.cubicTo(size.width*0.1852071,size.height*0.4563946,size.width*0.1830129,size.height*0.4561905,size.width*0.1807157,size.height*0.4557823);
    path_3.cubicTo(size.width*0.1784186,size.height*0.4553741,size.width*0.1762414,size.height*0.4549252,size.width*0.1741843,size.height*0.4544354);
    path_3.lineTo(size.width*0.1741843,size.height*0.4401088);
    path_3.lineTo(size.width*0.1876071,size.height*0.4401088);
    path_3.cubicTo(size.width*0.1888414,size.height*0.4401088,size.width*0.1898014,size.height*0.4396190,size.width*0.1904871,size.height*0.4386395);
    path_3.cubicTo(size.width*0.1912071,size.height*0.4376599,size.width*0.1917043,size.height*0.4363946,size.width*0.1919786,size.height*0.4348435);
    path_3.cubicTo(size.width*0.1922871,size.height*0.4332109,size.width*0.1924414,size.height*0.4315374,size.width*0.1924414,size.height*0.4298231);
    path_3.lineTo(size.width*0.1924414,size.height*0.4235782);
    path_3.cubicTo(size.width*0.1924414,size.height*0.4207211,size.width*0.1921329,size.height*0.4184762,size.width*0.1915157,size.height*0.4168435);
    path_3.cubicTo(size.width*0.1909329,size.height*0.4151293,size.width*0.1899386,size.height*0.4142721,size.width*0.1885329,size.height*0.4142721);
    path_3.lineTo(size.width*0.1855500,size.height*0.4142721);
    path_3.cubicTo(size.width*0.1844186,size.height*0.4142721,size.width*0.1835614,size.height*0.4148435,size.width*0.1829786,size.height*0.4159864);
    path_3.cubicTo(size.width*0.1824300,size.height*0.4170476,size.width*0.1820700,size.height*0.4187211,size.width*0.1818986,size.height*0.4210068);
    path_3.lineTo(size.width*0.1746986,size.height*0.4210068);
    path_3.lineTo(size.width*0.1757786,size.height*0.3651701);
    path_3.lineTo(size.width*0.1986129,size.height*0.3651701);
    path_3.lineTo(size.width*0.1986129,size.height*0.3814558);
    path_3.lineTo(size.width*0.1823100,size.height*0.3814558);
    path_3.lineTo(size.width*0.1818986,size.height*0.4030068);
    path_3.cubicTo(size.width*0.1824129,size.height*0.4017007,size.width*0.1831157,size.height*0.4007211,size.width*0.1840071,size.height*0.4000680);
    path_3.cubicTo(size.width*0.1849329,size.height*0.3993333,size.width*0.1859614,size.height*0.3989660,size.width*0.1870929,size.height*0.3989660);
    path_3.lineTo(size.width*0.1912586,size.height*0.3989660);
    path_3.cubicTo(size.width*0.1932471,size.height*0.3989660,size.width*0.1948929,size.height*0.3999048,size.width*0.1961957,size.height*0.4017823);
    path_3.cubicTo(size.width*0.1974986,size.height*0.4036599,size.width*0.1984757,size.height*0.4062313,size.width*0.1991271,size.height*0.4094966);
    path_3.cubicTo(size.width*0.1997786,size.height*0.4126803,size.width*0.2001043,size.height*0.4162721,size.width*0.2001043,size.height*0.4202721);
    path_3.lineTo(size.width*0.2001043,size.height*0.4336190);
    path_3.cubicTo(size.width*0.2001043,size.height*0.4389252,size.width*0.1996071,size.height*0.4432517,size.width*0.1986129,size.height*0.4465986);
    path_3.cubicTo(size.width*0.1976186,size.height*0.4499456,size.width*0.1961786,size.height*0.4524354,size.width*0.1942929,size.height*0.4540680);
    path_3.cubicTo(size.width*0.1924071,size.height*0.4556190,size.width*0.1900757,size.height*0.4563946,size.width*0.1872986,size.height*0.4563946);
    path_3.close();

    Paint paint3Fill = Paint()..style=PaintingStyle.fill;
    paint3Fill.color = Colors.white.withValues(alpha: 1.0);
    canvas.drawPath(path_3,paint3Fill);

  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
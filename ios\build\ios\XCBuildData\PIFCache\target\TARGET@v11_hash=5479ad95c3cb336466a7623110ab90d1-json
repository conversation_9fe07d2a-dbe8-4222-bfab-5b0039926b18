{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9b9c7bc6484816d92781127ce6d3e88", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9860e50b0828b44c508716f80e8b686be0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ded46b183b8d13cb364d67fe134bc52d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98277c127b4ce0236567f563b11c52b9de", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ded46b183b8d13cb364d67fe134bc52d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9807dbe5b52fe34917c7ad153d7421cafb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984be73f76038b3b27f87fb24db261c9c6", "guid": "bfdfe7dc352907fc980b868725387e98603268b620cdf16e69d067e5843fab79", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9861cf84439444ee12414bf2e9f2878260", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b37d3d7d385d2957041734c9ad9d8492", "guid": "bfdfe7dc352907fc980b868725387e9812c1d61775e7dddb4f49343085146b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8cb1810e67e8708d276fcd6fb7e481", "guid": "bfdfe7dc352907fc980b868725387e982d3db8708aa0ba7fb20ae657abd04331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb1f4ce575deef413e063362af8bc0a", "guid": "bfdfe7dc352907fc980b868725387e983acd98bbf1290eb7e111d3656e90c7e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d87ca60503612ee32e3352fb370d1f26", "guid": "bfdfe7dc352907fc980b868725387e988bfb32c0cc3edc9d8b00d9e3b2352f03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bb7366da4d34e64bc0b682b22417cbd", "guid": "bfdfe7dc352907fc980b868725387e984ce5a73513836100c863d8a4204d8a6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98233d7130ecac4f3d792d5d34c1f30d01", "guid": "bfdfe7dc352907fc980b868725387e984423956a90bb9977ba2cab14a81c4a53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fad733834e501d48f26b9b26cc20ed8d", "guid": "bfdfe7dc352907fc980b868725387e98d5d471a44537c5179db07934b32e0754"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801730ddf0c2a0806bf577d915bf215b2", "guid": "bfdfe7dc352907fc980b868725387e98726e683fb60a960c043be45cf786d7a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892341ded22412582f3628f80a102a3aa", "guid": "bfdfe7dc352907fc980b868725387e98991216713aabbf919efb9298b3b9d3b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d4cab1bb98e6253ccbd0595508ec24e", "guid": "bfdfe7dc352907fc980b868725387e986d98869e6b84d60c476042cb2ad8e649"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cde459e8a640af6411455bbc30d6944", "guid": "bfdfe7dc352907fc980b868725387e98df7ff8da18a81c977baf82fead3665b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98698c06b0609cdb61e474664d6fbda724", "guid": "bfdfe7dc352907fc980b868725387e9810dffb5444b6f3e4968d36b49c5063fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebbbf7e88baffff282ca7f35728cd974", "guid": "bfdfe7dc352907fc980b868725387e98fb0bd860c5a474541551899dae12b042"}], "guid": "bfdfe7dc352907fc980b868725387e98f87d75bdff4aa6fb86b581a84d01b312", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e983ac8c00eba506ae8a385d594878e5424"}], "guid": "bfdfe7dc352907fc980b868725387e9833890d2eeb24ad1f3fa7e644ad79a7b5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981f2920c8e4d4454b46a541fabdcaf979", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98ff6fd0f5bc9abe4e6913ce3d21c75783", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
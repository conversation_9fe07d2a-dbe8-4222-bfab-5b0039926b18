{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9b9c7bc6484816d92781127ce6d3e88", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816866bbfcb5c0dd318cf6ac162a31989", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ded46b183b8d13cb364d67fe134bc52d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b3ef6d86d10de0ea9c488a2e3838eaa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ded46b183b8d13cb364d67fe134bc52d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9880d98e321e19b0cc9dfd38b062b58ef9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984be73f76038b3b27f87fb24db261c9c6", "guid": "bfdfe7dc352907fc980b868725387e98c3a7e1b756c380244bbf996a70e386a2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9866790fce65fe925ca70081b01736053f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b37d3d7d385d2957041734c9ad9d8492", "guid": "bfdfe7dc352907fc980b868725387e98a5f87aab1b2f49dd6186e988a3a773a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8cb1810e67e8708d276fcd6fb7e481", "guid": "bfdfe7dc352907fc980b868725387e98e02b31bdaa01bec89f9978efd732800c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb1f4ce575deef413e063362af8bc0a", "guid": "bfdfe7dc352907fc980b868725387e98f5d60e164657a0039bec62904dca0635"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d87ca60503612ee32e3352fb370d1f26", "guid": "bfdfe7dc352907fc980b868725387e989b24c403321d301536933a1d7ed2e374"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bb7366da4d34e64bc0b682b22417cbd", "guid": "bfdfe7dc352907fc980b868725387e988801d30932a48993a5296e7989d74f83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98233d7130ecac4f3d792d5d34c1f30d01", "guid": "bfdfe7dc352907fc980b868725387e98311bc5e88ff43a28f2b1a4e47313f12f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fad733834e501d48f26b9b26cc20ed8d", "guid": "bfdfe7dc352907fc980b868725387e98d5b0481b3c89a1605e65f507aa9e1ca0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801730ddf0c2a0806bf577d915bf215b2", "guid": "bfdfe7dc352907fc980b868725387e987ecc36f5eba5bdfc8ec41b48890c61ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892341ded22412582f3628f80a102a3aa", "guid": "bfdfe7dc352907fc980b868725387e984ae45efa2700024b1e1de65e3dbcd22b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d4cab1bb98e6253ccbd0595508ec24e", "guid": "bfdfe7dc352907fc980b868725387e98c3210ab4cfcceb91b26360937d22410f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cde459e8a640af6411455bbc30d6944", "guid": "bfdfe7dc352907fc980b868725387e98db9759a40ec7ba350f6e8b8151f65a54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98698c06b0609cdb61e474664d6fbda724", "guid": "bfdfe7dc352907fc980b868725387e98d8d412e3376361fb08c07a8357492fab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebbbf7e88baffff282ca7f35728cd974", "guid": "bfdfe7dc352907fc980b868725387e985a6ba278d4bebee27842e325b188d119"}], "guid": "bfdfe7dc352907fc980b868725387e982f98f403ddbf2efef4330178cb961af5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e9890f054ba34b786b5d770587241199d91"}], "guid": "bfdfe7dc352907fc980b868725387e98f3fe88687ad6eff6bf37871ec575a1e2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bb795fa83338a31ff5f2e99d96bc0403", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9858f5a06028e05cea445233ec2481701b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98967a1ab7e97f3004b9cf91f8255af4bb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a9186989285bd998fb972ce277f4838", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4b797656b506af7ed08aee3eda8fd5d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b37a1f6412452e7499975f0eaf21275e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4b797656b506af7ed08aee3eda8fd5d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac2174ea8e92e04cafb435524caf8e4e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7b97b17dbf8d6649189728a8d48e0d8", "guid": "bfdfe7dc352907fc980b868725387e98408e096957263a5dd2d57544ac81f90a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b07fd387c660429bb2a94e0958c9bd", "guid": "bfdfe7dc352907fc980b868725387e98689e8f5d9d65ad21e5b61ec30f1f39dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a7efae70901777e4a49e2a3154d7268", "guid": "bfdfe7dc352907fc980b868725387e98e496c19c18612182233ceac967d55125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bf06fc570a49ac38a357458b08ee196", "guid": "bfdfe7dc352907fc980b868725387e98e88385533b65402ab909295f8d8bf483", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd5b4a8bea07ac78347d1eb9b1339d8", "guid": "bfdfe7dc352907fc980b868725387e9804394cb5e1d579817d12669031158fcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c4dc976a11c6743cff2e0930b514429", "guid": "bfdfe7dc352907fc980b868725387e98534ce7ae30363a6aeb511bc43f68b82d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987884ced1a2f1899ed8cc2b488839e2a3", "guid": "bfdfe7dc352907fc980b868725387e98e193f510114a0ed25adbdb086812d328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be4d978bb8eb55857c6688a00e083a25", "guid": "bfdfe7dc352907fc980b868725387e98806918b911a08c1e453bb6f8ec6b8e5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b751edc47395e8c7c9de88b2d5b4b7f5", "guid": "bfdfe7dc352907fc980b868725387e984a99f8973bf06fd7ecd48f47ea4d7b5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a1f97e7d9fd01b0d033ca3473296db9", "guid": "bfdfe7dc352907fc980b868725387e9865ab1f93c3e5dd8b94e8bedd8d50d57c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf64de3ff0c48b7ca9b777b170345b22", "guid": "bfdfe7dc352907fc980b868725387e988b2ae869c841d48f7621a75452f333a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da71b1bda263b17dd6cafd901ee8950", "guid": "bfdfe7dc352907fc980b868725387e988f34148a1805194b0e49f7beb7274ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985caf1949ae955cd8dfe12dc9c3a1fa21", "guid": "bfdfe7dc352907fc980b868725387e98ce08dc527a765043bcb0e25778b28a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b75f65788e2df2ca80d72cb23542ba1", "guid": "bfdfe7dc352907fc980b868725387e98a842eade1e03a1eacccbb6112e601f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899a3847be2eebdd6e96b1dbf740052c9", "guid": "bfdfe7dc352907fc980b868725387e98ec400b99e867ead36fb85a9ddc0dea3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980942e1ef276b707ccd4061a6ba4153c5", "guid": "bfdfe7dc352907fc980b868725387e98670eb1e671f51e4c05821918eb010481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaa7b7a814db0b45defecd3d826990d3", "guid": "bfdfe7dc352907fc980b868725387e98381389c6c65e012bc3f84136e47d6551", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f249c4d287c749f790c0b4b7c59367a6", "guid": "bfdfe7dc352907fc980b868725387e98890af04b86b386ff1c6ed75d7a84b324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98674b1c319bb384e8122ca992f868e173", "guid": "bfdfe7dc352907fc980b868725387e98eafe250e158a9866c042425cfe724ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983564d077a241fe93b477dde1870949d8", "guid": "bfdfe7dc352907fc980b868725387e98461b232e8ab9cb8320153ac40cf18b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851f731ee280c89672e48f0d246005455", "guid": "bfdfe7dc352907fc980b868725387e9898ff37f713170e9552f08fa30531b46c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f7c3725fd2334bc7e784a3abc8dfd24", "guid": "bfdfe7dc352907fc980b868725387e986835b8796a4c055252a4fffbf1d9c89e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983986b8f071c035e441a8ada740709390", "guid": "bfdfe7dc352907fc980b868725387e988cfb0d6febc566948737750f5dd9d5d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d9c86ca8c32a5cbe999119217bd929", "guid": "bfdfe7dc352907fc980b868725387e98d0ed8d0f8eb8a337aaf65c9f90d461a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863607fd35d40ee44d4fe54b735143678", "guid": "bfdfe7dc352907fc980b868725387e98138d4f541ae5519074e993023016073f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98216c3dca2d734b59f9acd12aab3fba48", "guid": "bfdfe7dc352907fc980b868725387e98776136c9ef87dff6a0c1e949f8c38ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803b5162efe410178e30f615d53ab405f", "guid": "bfdfe7dc352907fc980b868725387e98f8eb522b67fbb3d7943718519bbd4bde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac392242fa972c23df89dda389d810ce", "guid": "bfdfe7dc352907fc980b868725387e98d13bb27cd7ed169779d20b8c5f0b74bc"}], "guid": "bfdfe7dc352907fc980b868725387e98808e660503e89e55ed3cf0726d416c64", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a4f986df8868f3579525039dd6346fd", "guid": "bfdfe7dc352907fc980b868725387e9813289d7416f058b8bc83a87b0c859a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b452ceb2af714784074e04d2c29b84dc", "guid": "bfdfe7dc352907fc980b868725387e98a9ad811653e6da9c8d58d9707852e492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fb40451a7302dc6b5880ccfe8ba0b4c", "guid": "bfdfe7dc352907fc980b868725387e98b9d8f09917866faa3437e3f849511ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f921c4a276c6dea3d330fe0d179787", "guid": "bfdfe7dc352907fc980b868725387e988a4806d5c4d243010b2a2ca456edc3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c42d91235d632a7d03ae6c6ae0305184", "guid": "bfdfe7dc352907fc980b868725387e9875c77e2f0223b6a6cb60562c80152dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841b863bb2c611594ff346fbacdc52552", "guid": "bfdfe7dc352907fc980b868725387e98f41dec7891271341fc51ca9cd9b3de7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd8ca2e51cec7336195c47d249824bdd", "guid": "bfdfe7dc352907fc980b868725387e981158f7a402e3cf0e924b5ad2ccf21cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845accfac480b16832f412b2490979e9d", "guid": "bfdfe7dc352907fc980b868725387e988b25285695893c413044e3634fd52702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818ea92d029597f60c79f40a357d02966", "guid": "bfdfe7dc352907fc980b868725387e98d4824ec9d4112954acb01a6b3f2608f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e12487d1228c1ae9cda0e8f55a1a8a1", "guid": "bfdfe7dc352907fc980b868725387e98eeb59f21666a8287935a608091e475ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d56579b48d5a4753b8f97f396ef6f05", "guid": "bfdfe7dc352907fc980b868725387e98dd7346778ce40a9d9eff6785a27f08c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98966811f4ef18778b526718ede8e99b3b", "guid": "bfdfe7dc352907fc980b868725387e980de7382e22de2367c8a8ff9283195972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba63e579bf40f499b071c43bb10cd81", "guid": "bfdfe7dc352907fc980b868725387e98eb9aab8515725e70bf47b290f2ffbf9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e755631e9a7feb5c81c2f42a9d1b260", "guid": "bfdfe7dc352907fc980b868725387e9887170e2e2f0e1231e8f66fc27d5d75a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985296fcd2ca6dd90fe4386fe141df0f9a", "guid": "bfdfe7dc352907fc980b868725387e9895f19af6f6285e65ba1a6fc39d8d33fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895ab628bf98c555cadcda187437618b1", "guid": "bfdfe7dc352907fc980b868725387e98151e8f2dcd937577d9c166ff24e38103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866da97264618f4cbe478fcd12d482b55", "guid": "bfdfe7dc352907fc980b868725387e98adfcc487798a61542fe811b996202b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a907ad2715d12c10dc1ca31a5a7f48da", "guid": "bfdfe7dc352907fc980b868725387e98bfc667348db266d8236ddf09fe3f687c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b627c4295a5cf1495a3187efe46288c1", "guid": "bfdfe7dc352907fc980b868725387e989bf3642b2c1c77a0defaabcbb23f9bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813da0280e07d835cd571eb5cb84029e4", "guid": "bfdfe7dc352907fc980b868725387e9814ca939d7ef14a75c492b565d9ff81d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62fc0a052b7148cd07a6c28d4b7bfd2", "guid": "bfdfe7dc352907fc980b868725387e98bcea5a5e9b0795d87ad828228aae02c4"}], "guid": "bfdfe7dc352907fc980b868725387e98af7c298017aa361478f3e0825042a7ea", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98850b05f3204449f19f358f0f3dc05b66", "guid": "bfdfe7dc352907fc980b868725387e98eaea5eaf3300cd8b30cd03886bb61628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884bfbca5c8afd67274918042f4f7fc50", "guid": "bfdfe7dc352907fc980b868725387e989f4403d826ead7e7553d2be609e28404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98e4ac94164d6b5fc457d5f175a5efda42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98328c5b66ab8a17803370a7c58c5aabc6", "guid": "bfdfe7dc352907fc980b868725387e98342fc37c026beffd967da85b92328ff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e9877f340a3cd66ec724d38f2560ed29b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec82dece765d451054cf9546cb2f0d4", "guid": "bfdfe7dc352907fc980b868725387e98d6420868497ac30ec74b165bada2ea66"}], "guid": "bfdfe7dc352907fc980b868725387e98fef37a92c516d72d02a173ef676fb740", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fbb22bb0f5869f65ac700b94ebefa304", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98a8d455f06110aff21c29e94460d34072", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b04e6d4bde46472a635b058fabcb5059", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9834d35479da64bfde566599232b3f74ab", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ad62e6dd64f4435461867290707327d2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9821d6bd3c633d882c00f5cc37c11b9126", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ad62e6dd64f4435461867290707327d2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816abf9c98737df05feb4c6424504e950", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98131e49f547fb854bc990a0f9962cc886", "guid": "bfdfe7dc352907fc980b868725387e98f5ea80967b3932ae092f43cb70f6ea8c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98729763aecdfce2ebe0b7dae0142d29b0", "guid": "bfdfe7dc352907fc980b868725387e98e3cb7cec46e93c4f8159e299380a65aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989427c66fd5c693f93de135295d25e56a", "guid": "bfdfe7dc352907fc980b868725387e9862f3add03409c7675cf42ff7a23ec7ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6380fcda1faae5ef4770fe540fba50d", "guid": "bfdfe7dc352907fc980b868725387e98e62cb799ceaa9787efff872486348fd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988454ea075c7b2812b3f8738427a44ac9", "guid": "bfdfe7dc352907fc980b868725387e98863967ba6386131a9363ac3c9e726dda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824e5f7342d1473d9ae338296e8bce73b", "guid": "bfdfe7dc352907fc980b868725387e9863c4745279af96142e883318661fed85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc5c82fa7bd7bc5be9a8b71368bae315", "guid": "bfdfe7dc352907fc980b868725387e988eb779fbd444607482a041a1436f6112", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d99a4fc2208062c18cf39e237b2adc", "guid": "bfdfe7dc352907fc980b868725387e980b191fa8a892deae1b2a7aac64f3304a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b48143ed2e6a9ddc3c6653742bbaab7b", "guid": "bfdfe7dc352907fc980b868725387e9886d9839194ce9bcbf6ee9f6835aaf608", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fb00d610c736a5a78ce7cb197440e85", "guid": "bfdfe7dc352907fc980b868725387e9837c0855f8dea08036ee382e0e0fb9869", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986010e57683e8c2d32409c808c46170f0", "guid": "bfdfe7dc352907fc980b868725387e985a8edc5042c707db537b2f80ddcddca0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28e411f4aee563894628dd890723408", "guid": "bfdfe7dc352907fc980b868725387e98a25ac4698365765fe18cd4e79a7c4959", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838239098db16adbebb41322a94f1c068", "guid": "bfdfe7dc352907fc980b868725387e98d7ac98c35e9f71b67ed2aec2a4d35594", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98775c0346941ed7f4af03b98e77826304", "guid": "bfdfe7dc352907fc980b868725387e987ce2fe2139aa054ffce0ee53a793347b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29b4e37e7afa05fb1d432a6bab029b0", "guid": "bfdfe7dc352907fc980b868725387e9883a0b8a19a6f5cddc0707683d129ff75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fc0ba8e952daccac49b2f92676b5bdf", "guid": "bfdfe7dc352907fc980b868725387e98e025fd266d4737cc51e49c9eabc3e7e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850163aedb90146c2dc7358af9c44b145", "guid": "bfdfe7dc352907fc980b868725387e98817b9575e941494c5661b4885f8b84c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841fce8ea10728cb4755a886df1f6b8d5", "guid": "bfdfe7dc352907fc980b868725387e9814858682ac257200a0b90229baf485e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fda8a814db5300b8ece78bbb90c4b78d", "guid": "bfdfe7dc352907fc980b868725387e9862b7f675666cd3087538a2a3e88375e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d0fe815125037263fca7e62fb49800", "guid": "bfdfe7dc352907fc980b868725387e9884e7a4fb5999d864a736944837faba10", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e74d15bf678b9f667fd7b1d4a06fd30d", "guid": "bfdfe7dc352907fc980b868725387e9801d1a247c8f3d05851b499dd3a5a126f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e49797b82db194f5508c061377e6e6c", "guid": "bfdfe7dc352907fc980b868725387e988fed1a64a064052228dcff73b6f8b008", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e99266287f97928dbb1b47e67c6ba43b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b56aa3e676083b537337b67fd236255f", "guid": "bfdfe7dc352907fc980b868725387e98207a831d69ccc0b4233635c190a759b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9421014c9fb404f6b1246b5576af72", "guid": "bfdfe7dc352907fc980b868725387e9876df7263c38313504198a469f8bcc9d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a3d9016bb6b845995e474bdda6b8ab1", "guid": "bfdfe7dc352907fc980b868725387e9883e6d6dccb9ff31eda55801c309867c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf6f4d5bfcb721e58bbca37a68c187d", "guid": "bfdfe7dc352907fc980b868725387e98eea6eb2522b33aa746845d04fbd043ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ede6ee848d6427df421a96b86fa5dde6", "guid": "bfdfe7dc352907fc980b868725387e989e7c0786d8591727c06dc6e3317dc461"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873787769b0fc696a9843e58b31a85476", "guid": "bfdfe7dc352907fc980b868725387e98647eee7710e9c2cc02d76282dc968118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986368e74c0dc47726287ac0ff8b0ba112", "guid": "bfdfe7dc352907fc980b868725387e98127a2533d58b2d31315ca3378de1c0a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92b5b5338a9b2cb7dc7d8361c80c8f3", "guid": "bfdfe7dc352907fc980b868725387e985445251383b1bf80cc8a060113e48a81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981caff4a2fa9f26fb57cf8cb5b7abca65", "guid": "bfdfe7dc352907fc980b868725387e98b147a586042c3297d2e42d384dcb6b59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f7217f4c4ea8c601c46b373238eed4", "guid": "bfdfe7dc352907fc980b868725387e984d199b2cc02bbbe43f4abffa1734c3ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c386c937ac295d9f7d67d3491b314cd4", "guid": "bfdfe7dc352907fc980b868725387e98c13c4f201a4edda0d7a3876e0be403ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f0cf8c6e8bd1dc2e4cd3179bd00c7c2", "guid": "bfdfe7dc352907fc980b868725387e98af1a5e8ab94a12e19d2356862a15287d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98347cf9bab1d92ec6aca1b5e9fa82f61c", "guid": "bfdfe7dc352907fc980b868725387e98b1f16b3a93aee4af00f01bf899e18075"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982533fd392c7cfcaec67d8e3a4731eda9", "guid": "bfdfe7dc352907fc980b868725387e98bbb16fa92e9a52dce433bbda423595d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a58d017b53fa88aab8427b2266910759", "guid": "bfdfe7dc352907fc980b868725387e98fcb1c2263f9eec82b2edab686721f306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee968266e79acc16cd9f52b45707d057", "guid": "bfdfe7dc352907fc980b868725387e9882e581197555606c2109e78f1b3426ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7bb63d282ce1deee935e419630c2504", "guid": "bfdfe7dc352907fc980b868725387e9822adb8e7b9c57f8788cf0eb27ca21966"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1176c447db097cebe865e90ce77ae1b", "guid": "bfdfe7dc352907fc980b868725387e982314afb57b9d0f8aab660eb7170aa02d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d8637ed3dbb074d4c280793a0cc9f4e", "guid": "bfdfe7dc352907fc980b868725387e98a5073f7da3b3b352e24374e2e4547b89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197e9da3b8a98f06c5338afa2575d051", "guid": "bfdfe7dc352907fc980b868725387e98d0f12c7c1cc9e51d625f09274a75832e"}], "guid": "bfdfe7dc352907fc980b868725387e98f894ca9b11cba3cc7e480cc2cee6f527", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e987899564821a3f486c3d2214a7fb50bf1"}], "guid": "bfdfe7dc352907fc980b868725387e984fea55295a120438cf202601650f05c8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d019290110368614c0a2177448bef5aa", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98cbfb7ec993eac8451186bd2e1d474d18", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
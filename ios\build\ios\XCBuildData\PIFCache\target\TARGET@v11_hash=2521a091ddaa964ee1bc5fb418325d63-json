{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98529faa0be728dc8190114fe28eefc098", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1cc874a020a479547cea61eca3df2b6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e56f5ddefdf0b372a6ca52c7ce1a51a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d47dd7ba22ca9f0b4fa96fadfb06490e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e56f5ddefdf0b372a6ca52c7ce1a51a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e6188ca4110dd4031a8a69eedacc01eb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98522cb7f15d9027022fe9d0aa65baa7de", "guid": "bfdfe7dc352907fc980b868725387e9857f3f3bd87180e1fccc9d5b1d0ecc539", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f624658d19bff25eb37e26350db9ac60", "guid": "bfdfe7dc352907fc980b868725387e98f2ec65b19effa60e849cfb63135f5d19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bf639ba47b8c79b9bc359089fac57cb", "guid": "bfdfe7dc352907fc980b868725387e9890f20e55806666a484343d9cecf2cf7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc69ccc79cad80065a6646f492bd1836", "guid": "bfdfe7dc352907fc980b868725387e984dfd1caa427974dcca1de82ce1639d9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841b454202dc1d26765c56ebeceb76b39", "guid": "bfdfe7dc352907fc980b868725387e98c176b0543fe64a6a98463fccd3d03dfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f6d7333cb76278208cbb69dc6735e9f", "guid": "bfdfe7dc352907fc980b868725387e98b6acbeb0c5313cc8ce28ba95371b9133", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a325c363336f7b5d148f7503398d0a1", "guid": "bfdfe7dc352907fc980b868725387e98050805f4be9a502d611240d2fa0cecd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98550f7c5569294dd9bd600d0101f9e5ca", "guid": "bfdfe7dc352907fc980b868725387e982285b32ebb373c605896886ef6bd5e12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dac37f218e9e9c6be928910473c7fe9", "guid": "bfdfe7dc352907fc980b868725387e980df22dc5519eecc8e2e29ff85b4e0403", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984983f37ea7d9477e778f43b889cf0884", "guid": "bfdfe7dc352907fc980b868725387e98c6add1f4b74d9dedea30350ba025926a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884db852714738b7d7483f9620dce1289", "guid": "bfdfe7dc352907fc980b868725387e98f97d78c653f7209fcc5763f86c31607b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a41f5ac72b9b0642c27797374ef67713", "guid": "bfdfe7dc352907fc980b868725387e98e4935c01328af474ee30044d22068e66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98640a873dcc0e05205c78946b9d78d549", "guid": "bfdfe7dc352907fc980b868725387e98b5eab8227c90347b2b378fb7785451af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caecc6d707c174fa9f1594664ef28780", "guid": "bfdfe7dc352907fc980b868725387e98d573aea95296f04f88aa326dcf8a93ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98367999f8d88e33811c83786baeeef179", "guid": "bfdfe7dc352907fc980b868725387e98d0cc481a7aaa506e37521d62664e279a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829f7033781c352c6c5f1356f81d8477f", "guid": "bfdfe7dc352907fc980b868725387e98229521aa2b4b32d588db97644429c411", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982a1d921d86947a34d02991e7a955939a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98513c943f84cbb9d31856ce09da2f6be9", "guid": "bfdfe7dc352907fc980b868725387e98abfacce0ea3d3dc7a80906e1cced2367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5766edb93809a7d9d6e5bf2035e3713", "guid": "bfdfe7dc352907fc980b868725387e9810cfb9f6f764ce729df83910ebeaf551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ee660664188e2375e93b7bea08f463b", "guid": "bfdfe7dc352907fc980b868725387e98a959dcf6b58680a91749fa268a26d661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98183ff99a2ed6116debd53fb4739b72a7", "guid": "bfdfe7dc352907fc980b868725387e98124ffd83f4fb2de8b3efe57610058d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0b2b03476bea35d43260f05f9788505", "guid": "bfdfe7dc352907fc980b868725387e98f7e2fdc5fa895ed58141c8c308102c62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa35eb93e1c1ec0fb164deeda1881ab", "guid": "bfdfe7dc352907fc980b868725387e98b4bc26af3fd19d0fa29f663a24c777b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de3af8897fbc657a4c4fd5beb2c75458", "guid": "bfdfe7dc352907fc980b868725387e98c59068fa8503cad50b4ec66ba6fb7142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcfea058325c282e34a535a0dbc89be9", "guid": "bfdfe7dc352907fc980b868725387e981c4131d91b18383c2fe590d9d5db2728"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881aa27c2e18defc6f6f383b3b28446ba", "guid": "bfdfe7dc352907fc980b868725387e98275d84b6993e46a75067b2e66e37cc23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7079f15ce7482895c6a6d8f3205b556", "guid": "bfdfe7dc352907fc980b868725387e98f9fffbdfa7a0cc1fa36450812b129354"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0126c30dd3acea861f1e921fa1c221b", "guid": "bfdfe7dc352907fc980b868725387e98f6c13bb5326e9b1d19cffd4f8b70ae8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dfb73e49909241d3bdc2d0f983abfe7", "guid": "bfdfe7dc352907fc980b868725387e98749d66167f8a2c858175ff07441f2bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985afe1263923c07b25f6d6c58b24e8e9f", "guid": "bfdfe7dc352907fc980b868725387e98f60c4da1d30f5c651773fae03a95a1e9"}], "guid": "bfdfe7dc352907fc980b868725387e9888ddff36bfdb8fba1896fffbff290975", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98e21cb5e95267ac01a60b3f75a1af0e91"}], "guid": "bfdfe7dc352907fc980b868725387e980e1ce268bfb1c82d099ffdb3b17b4474", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e20954141ebd835c2535267f91ccb9ea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824ba432227575199f2dfbf500ac071fc", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d218f444dac2b432a812341fe895d269", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d218f444dac2b432a812341fe895d269", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98902bf427c2e1000ef29a21b5694f2816", "guid": "bfdfe7dc352907fc980b868725387e987b2a96482406cd48d3c5e964dbef8bc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f0a7a010969810b2f638e9203f5b793", "guid": "bfdfe7dc352907fc980b868725387e9834bcd61548e1006692ffca0df79d05ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822f070c6dd7bf77acee67d80a32bbef4", "guid": "bfdfe7dc352907fc980b868725387e9826d0a88b076166c4b864cdbdbce92bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46b2daa7a7764e140bb88dd072c075e", "guid": "bfdfe7dc352907fc980b868725387e98f06dd451ac8f1a44c57764e553b527a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f85cac93fa17fe405921c0cf45d82f7e", "guid": "bfdfe7dc352907fc980b868725387e9858166e44940ebd93c8b5a766b12b84f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfd8875e61c3d84a508dd417181dc4bf", "guid": "bfdfe7dc352907fc980b868725387e985bf0c4cd98e92817abbdcc35bdc5531f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae57943793993a3256912a4271e5db94", "guid": "bfdfe7dc352907fc980b868725387e98c51f26b87df945a97112699464d5bdf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de43f0873c0ea7f31ccc2cc1b1aafb3f", "guid": "bfdfe7dc352907fc980b868725387e98af4c2ceadace42a2f4adc0d63da70812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986377c00e98365ac6913fb4a8e6d54448", "guid": "bfdfe7dc352907fc980b868725387e986dc9311543863ced0c90776181c9ea38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adef298823fdb585d19bfcee9426883e", "guid": "bfdfe7dc352907fc980b868725387e982c85902d07d31bb1e5ed529b4fec6022", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dadf125362c9d4a6e6ab84413e8f09b", "guid": "bfdfe7dc352907fc980b868725387e98a00ffc876eed62fce6ba5794f8479b12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98244e9d3d4ac0570568bc1cc9b2152044", "guid": "bfdfe7dc352907fc980b868725387e983ad80b28dc127b28a95d9b21b3eba752", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e11db2434ff954abb8bf174dfd37403b", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e9d2426f0b57fcc343d00c6325e232", "guid": "bfdfe7dc352907fc980b868725387e986c3f6d4e621a705b01b32cad0135ff32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc78e799d14c3773ea0a62edf137116b", "guid": "bfdfe7dc352907fc980b868725387e98908859acf0314667d813df278ebc7971", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98273430cdf913bee772459f4a843d5365", "guid": "bfdfe7dc352907fc980b868725387e9835060e3266401f00c0e55803e560ef3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981004975434ff9abdd70f184db969365f", "guid": "bfdfe7dc352907fc980b868725387e9892c65e1f99e88c517940337dea3b8c13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98178022346f7dba473f8ab04c21ab4b0f", "guid": "bfdfe7dc352907fc980b868725387e9870e3688111116f66aea186a02ca7fedc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afa6ea1d016c66b2b4a1385e7b446300", "guid": "bfdfe7dc352907fc980b868725387e98d8787100502fa9502542bcd3a823833c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98624280d154555b8b8029d60a48697777", "guid": "bfdfe7dc352907fc980b868725387e988052de86f70283058d324ddc6ab5acdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846aa84c19568d7a22f8332a8ba174f96", "guid": "bfdfe7dc352907fc980b868725387e9872a4c5a4082445e692c9d19c755c8ca6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985897c177bc08378c7e7b378c8befe8c8", "guid": "bfdfe7dc352907fc980b868725387e9804b492d3ba5a3c526fd4ef87a8c1332c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c818238ab482ae31617aa1c54b94a60", "guid": "bfdfe7dc352907fc980b868725387e98a6052558bee4cf5128d3d0f542c926ed", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e950b82ce6c467e0a5c9ac8f29edaa0f", "guid": "bfdfe7dc352907fc980b868725387e9849d0047527a358bfff8af909b731bda0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829a5b5cd4886924aa2b442fb51776cb3", "guid": "bfdfe7dc352907fc980b868725387e98058b771dd892aad80c349da616705ad7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d648b77d85c566fed64a841e105df057", "guid": "bfdfe7dc352907fc980b868725387e98a33accb89e3fe72aed974b9e3f2fa683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855f7179e6df43cf744b03b5db0a3d91d", "guid": "bfdfe7dc352907fc980b868725387e98531d786c9c0863ff8e634db7caba945e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876a42e9337639cb95f4ac301208e1064", "guid": "bfdfe7dc352907fc980b868725387e9809daffbf8d209e4827b067f350b66ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98214857ea180ec489d19f01e204f4352b", "guid": "bfdfe7dc352907fc980b868725387e981ec6e69e78436e9f90db8be66c2da384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ae40d3e311f5dc12bde937e803f9fe8", "guid": "bfdfe7dc352907fc980b868725387e98e87b27bbc76fcba88e2da7f95c269456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4cf4870936e3b76851df7a9290eabb6", "guid": "bfdfe7dc352907fc980b868725387e9898a2705ad2089817ede15eeeeacfb22f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98078ee3ebb5af19640a0ac17ac55befa3", "guid": "bfdfe7dc352907fc980b868725387e987b1fe7dcbc4de956ab960b813043de76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98632af5d89225cb2c12a3fd80be1b83c6", "guid": "bfdfe7dc352907fc980b868725387e985e6b60e54a68d747cff302ba76b28f72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878cb4574539135e5c0a40a2a7e3da16c", "guid": "bfdfe7dc352907fc980b868725387e98b02270ba1701e242ede905bb791e4bb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e7dbb80be6687f112a71fee3d3ffb6", "guid": "bfdfe7dc352907fc980b868725387e98a5300e0a36e0f37f36453390299cc559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d0d93d1a812d7f6f01e446869f2be8b", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849f27bf334a290a84bea653d13029fed", "guid": "bfdfe7dc352907fc980b868725387e98187ffa737b89431fd12596f736d5b0ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c098c3115b5309c166ad34fba1feec96", "guid": "bfdfe7dc352907fc980b868725387e98df7b7f5210ab096b5b90dc28d6a1ff40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f72ebaf931b850fb9dca61428ae2dcb", "guid": "bfdfe7dc352907fc980b868725387e983cfef307d010a7dce17c978e0e3a8387"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a317546fe24a7a23a681dc3435b95121", "guid": "bfdfe7dc352907fc980b868725387e98e02cbf019d1e54682905237fda670b58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0cc011652b4e9d3416f6a246683e4f0", "guid": "bfdfe7dc352907fc980b868725387e98a58a723c82c2b84fd623236bed4e346b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6597ae1b5f69db55e5bc927e3ebc8f8", "guid": "bfdfe7dc352907fc980b868725387e986f4b52efa0acd85e7ad08118a848fa36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aed2369cfbaec09ae0d969b2e5088af", "guid": "bfdfe7dc352907fc980b868725387e98fe02c6ba8bbe97e0b7513989d619e5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986babd885ceb8b5b45d2fd56a351f97c1", "guid": "bfdfe7dc352907fc980b868725387e984f1bf9fec22522dbec969a28b45a6acc"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
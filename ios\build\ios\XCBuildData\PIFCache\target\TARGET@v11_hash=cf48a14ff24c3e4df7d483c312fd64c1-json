{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2089da8c9df43730179623ba6496131", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1ff9321559a86042636004f92d70c3b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1839952a36aad9d7deb46721e2489b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5b9eb0258701c449dc3f3f3c655eb22", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1839952a36aad9d7deb46721e2489b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f806167435500197e4869cdf1a4e0b65", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9319dfeb8c797bbc8ae433d3fc57696", "guid": "bfdfe7dc352907fc980b868725387e98806d577bad2016bb80bbae4622f5b0f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e9e204270d05393321a914721582ab", "guid": "bfdfe7dc352907fc980b868725387e98b52aeddf01615b933ea3f7b1301bd5d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982682d071edb892de49f287c14c037045", "guid": "bfdfe7dc352907fc980b868725387e981f0c4ee31df4effca09f018d090901d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98917f1e6f7b5f50b05b236c356bc29485", "guid": "bfdfe7dc352907fc980b868725387e984bd29d1c5424203a0a8dbbe89a2d5b70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810eaf9ebc7521ab5ecb45a448f7e0d42", "guid": "bfdfe7dc352907fc980b868725387e9820a253d24d61cc6afe2ec71ac6f165ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b3cf3db3ba09ef225dc7c6b70843d52", "guid": "bfdfe7dc352907fc980b868725387e98c024b990787ab9cb3e90a4dbc9ac4978", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac79249ed8cef5bb8553602961bdd21", "guid": "bfdfe7dc352907fc980b868725387e98066c2c1add0e89de57b36d0e29a1e87d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98224d0112748ac691dd1d3ba5b5c662c4", "guid": "bfdfe7dc352907fc980b868725387e98631feeb5266786d1c3bb0a55d76fda01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980924398cc4552e89d93b3f47fa5459b6", "guid": "bfdfe7dc352907fc980b868725387e98ada9d39588245fa6085f8cc6535f4d60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826e725600a0ebce0a54ad0ef8daea903", "guid": "bfdfe7dc352907fc980b868725387e985452e4e134f034492bcb006d6682ac5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db31b40952c044fd9b4c0ce8b690ce37", "guid": "bfdfe7dc352907fc980b868725387e984e6964ce4cb18daa9e642463bde705cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eccf5512a192e9a2126d38fdf6a2baa0", "guid": "bfdfe7dc352907fc980b868725387e982854599d643bc39e9b12d34d7dae1e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c41e2d79b31efcdd12f29b9822c7ddbe", "guid": "bfdfe7dc352907fc980b868725387e9805adfe8c34e3f169ad5498ce4abbd97b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b992e091e569a883ad49a359692b6d39", "guid": "bfdfe7dc352907fc980b868725387e98302a5b19e7005d8592622a5cff8f95a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838b11b5aa1c904ac59f0decc9fead0c2", "guid": "bfdfe7dc352907fc980b868725387e984e5147a47a180cfcab2f0bf6b4426416", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d0bbd687c3c7e65eaeaa3820f439ab1", "guid": "bfdfe7dc352907fc980b868725387e98188c3d9573569928f5ee9f15726c3908", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c4faa73615efd240b8f2532e6832b0", "guid": "bfdfe7dc352907fc980b868725387e9868db6b56461e051eaa26c19b11a2a643", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986521dc974f1091b40bc72a46bb7a042f", "guid": "bfdfe7dc352907fc980b868725387e9831eab19b1b7473bb88ed5bc194390bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cace17d066958f6e1da7aa582bfdf483", "guid": "bfdfe7dc352907fc980b868725387e981128dcb7a549845e4bc592abeecc459d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1be1013957aa51ba25beabed94fdf1", "guid": "bfdfe7dc352907fc980b868725387e984fc6c0daa1b25e893c4ecec825a42e9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873fd264f398b17a70310df0de795bd18", "guid": "bfdfe7dc352907fc980b868725387e9858d41b3c378c78b69f61ac72915fcb83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804589b8da614a853dc3919f72cc861be", "guid": "bfdfe7dc352907fc980b868725387e9822e8770adc4cf059bdf5b3f4dbfe42ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d73b5717e2579103934a989784c1a5f1", "guid": "bfdfe7dc352907fc980b868725387e9811cd6b2970e9b422b995a69528369665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9cc3c1a422122ddb01b4d4e856d09d9", "guid": "bfdfe7dc352907fc980b868725387e98cdc65435808a446c0643c07c34f59585", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982730b0a5cb6a6b3d294b1ed019f0351b", "guid": "bfdfe7dc352907fc980b868725387e9894f3a21cca31e1a09fcdafeaea72ab80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98525ec91d6a279b8450bcad9ca025d3d9", "guid": "bfdfe7dc352907fc980b868725387e98082cf411fcbe74fb23a61afd9bedb1a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c47ec986d3a7c9a9dfd2d168702cb41f", "guid": "bfdfe7dc352907fc980b868725387e98d54e69fad7910d525524d112c6e7d2dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98205692f396826f019eb6795db3026c84", "guid": "bfdfe7dc352907fc980b868725387e98f57c60869a755422a10504d247af146a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985223edfc9c82e79d5c7c25506d8d5de5", "guid": "bfdfe7dc352907fc980b868725387e983a0f7028169cc406eb03bc300345650a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853509e995e161b31dcfc5ffd0f5e3fd9", "guid": "bfdfe7dc352907fc980b868725387e983fe22d2ac352d8046628d96476337160", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de75b1e41e6282d824350cfd16ddab76", "guid": "bfdfe7dc352907fc980b868725387e98448bfacde8c2dc67d9c1534e20f44fd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78481505832f7f5d73266c1d255beb8", "guid": "bfdfe7dc352907fc980b868725387e98ba2f9a3057cc1f7dcd22cb70ddd79daa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ea3a563292a5e722357711c2b5b5713", "guid": "bfdfe7dc352907fc980b868725387e98cdaaf6ba907e44c7b82a0401bbde2a98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c02238a2b25f42a34665a2892ee60d", "guid": "bfdfe7dc352907fc980b868725387e987c36f34a5cd3f35d8c57c8f7eb043ba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d33d6a66a94a8ddbf11189463b42ca34", "guid": "bfdfe7dc352907fc980b868725387e98b4295129df590939e25c137f47ccf1f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d2af452e85bbf482efc1fb1f93dadd9", "guid": "bfdfe7dc352907fc980b868725387e988abed2dc3193f070f0cf1a3fba2fdd92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f641ea77a928ab524ced504cab1efef8", "guid": "bfdfe7dc352907fc980b868725387e98ada10369131e74d1b788a33f7dcd6e12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98501fc5a825868f7d87159a7ab617edd5", "guid": "bfdfe7dc352907fc980b868725387e98f9df3cd61852b428dfa680bd35ea754b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982192e6fba77763bcd4ba20f88dc412b9", "guid": "bfdfe7dc352907fc980b868725387e9898b212dc8787ce3d4b7fd6a87a0a4415", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c03ebdcffba736455ccd4d50dab78e26", "guid": "bfdfe7dc352907fc980b868725387e98b6cb90ce781b07e5914863ad71fa8377", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b227f1684156cd4c2acc15e65567f3e", "guid": "bfdfe7dc352907fc980b868725387e98a89e2b5db28b4c2e870a39ff023e4205", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985968ab8b5d083758a0fdd291a090dac4", "guid": "bfdfe7dc352907fc980b868725387e9853c68b4efc105e2de2e7d3ceb51e9aac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd03cc24f364c95049407fec50521591", "guid": "bfdfe7dc352907fc980b868725387e98a8473b3693a6ee75ede363a092b64add", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98260d71299cf0b0ec7d6160a074dec011", "guid": "bfdfe7dc352907fc980b868725387e98f170c1345a02f6fa218d9153e7fc8b08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a631531623ff08540670f172a80046ab", "guid": "bfdfe7dc352907fc980b868725387e989d2461733f5d7437ce547fb0f0bd4558", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c5ea82cf2eb447a4cc330f2f71553653", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986bdc8af24dd2023ea840066a11ac5e4c", "guid": "bfdfe7dc352907fc980b868725387e98b33f86cb67045f508cf2ace5a98d6bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b75e0afbbca2541e09107d608b4acf8", "guid": "bfdfe7dc352907fc980b868725387e9816b1bb8c09b939ba28fdce5a0b98a268"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca3583115842a78c5c5364da35f896c", "guid": "bfdfe7dc352907fc980b868725387e9820dc39a5d544b98fd5e56724280ccf59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825b9a5087e0db4cc1a3f1fc181b27bfd", "guid": "bfdfe7dc352907fc980b868725387e987fea851d30d2e76ca37fe8cb655f6cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98204a95feedf9e90d1de1d651c307b6fc", "guid": "bfdfe7dc352907fc980b868725387e983e7d9aed5ea8dd70a745196c3fde312a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98862a869139ebb7d03011c94a9b55b4e9", "guid": "bfdfe7dc352907fc980b868725387e98cc890457a2f6ee0481b12d5821c2f63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98085b9b6a562ad94a824d6586d7be1c78", "guid": "bfdfe7dc352907fc980b868725387e98a2cfa2a86b5160ed3ab4b0751e230e44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fe1eeecbdcdff936819dc140f46aaf1", "guid": "bfdfe7dc352907fc980b868725387e987449a7d3d6711189c31322c4a363ddba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd846b75909454116b599ec698a0a59f", "guid": "bfdfe7dc352907fc980b868725387e984560ced14dd67a0b0b0aa9351d88659d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981afe2b11f0182e8eb4a129ca6046f031", "guid": "bfdfe7dc352907fc980b868725387e98205d03e4651a24e7b82c9efed3955094"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d032130508f5067830e4ac309bfa602c", "guid": "bfdfe7dc352907fc980b868725387e98d07d7f42027750948251986290dc286c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98117ca95fc1591593a94fe0570bede978", "guid": "bfdfe7dc352907fc980b868725387e980aef27fc8b4247a5df43db05e928558b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d2ed0ea089a96b1438d9a6a8b82bd8d", "guid": "bfdfe7dc352907fc980b868725387e98f27d34ee038512bf48712b585743a4c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78e85aaad8f7946c0dee43dc3244652", "guid": "bfdfe7dc352907fc980b868725387e980eb550bcc03a690ffee730145a3883d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4bdea5ac7b065dc4306e5bfc8da3f54", "guid": "bfdfe7dc352907fc980b868725387e98be83f66df92c6bf6fd4bd75dd680b3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981729e2069b993b902a6b3e4358571a13", "guid": "bfdfe7dc352907fc980b868725387e98616d57624f2ddab6779ec3118a0b11aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe30d39179a3b32bc4846071d0ab723d", "guid": "bfdfe7dc352907fc980b868725387e98010d359cd88ba87b6ae74f76583700f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823837528c94ea91252574ab8f1dd6e56", "guid": "bfdfe7dc352907fc980b868725387e981b38d5d55fde4d9fe1fde443b8a34c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b27997b310855f4a9bcbd98e6b457ae9", "guid": "bfdfe7dc352907fc980b868725387e9814865e40f3a6969bcf218e4f80cae81f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae3f584699d1d94b4c060ed4805ce03", "guid": "bfdfe7dc352907fc980b868725387e9897b970cd6c69b01f4c1406906297cc16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b0b7c2b532d69aac74875d9c92b168", "guid": "bfdfe7dc352907fc980b868725387e98ca499965967e8cbdafb5206b2bf3caa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859cb471a92f11f67ec5e50b87af85a3c", "guid": "bfdfe7dc352907fc980b868725387e9845230914e97a9b04dc0fc28658e2bd67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da911bd087abbbbccf29c9494804967d", "guid": "bfdfe7dc352907fc980b868725387e9894e802b6dfc20de05da7c8b6f92afa12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8184f93907417ee28961456b4479455", "guid": "bfdfe7dc352907fc980b868725387e9850112d6b0263163250ac7e0216ee460c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a4dd3e0f89a95610eca63409f3ad0fd", "guid": "bfdfe7dc352907fc980b868725387e98ae312ffcb316a9955768bdc4ea82df1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee603cd02b9ec56e7af2ebb1d29b35ac", "guid": "bfdfe7dc352907fc980b868725387e983275b241aa919477874218fd448bffb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b0958e3028fcd0e9771fea1830f8ac1", "guid": "bfdfe7dc352907fc980b868725387e98b30b6825f205b76836fb475ed9a4fc8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c627fc50216eaa93946d5d2398ad98c", "guid": "bfdfe7dc352907fc980b868725387e98002743e5ff7bb69a9944eea5373b7602"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f815aea57b358e291902ad65348649aa", "guid": "bfdfe7dc352907fc980b868725387e98314c0bde4525ecf85bbcd48043b723f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8aaaac19db7be553416876d3c8b1fb4", "guid": "bfdfe7dc352907fc980b868725387e9834227b514fcdfb509a53694ce72ab88e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801b665ec0881731a8182128bbdea9ec1", "guid": "bfdfe7dc352907fc980b868725387e980f24297b47a67a8153c561ca8afa8397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d5a4771c037e2dfb81cefd92281659", "guid": "bfdfe7dc352907fc980b868725387e982237441783b6acbcdd3fb1dc435f04d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3bd9b7d981e0a60ebdbb41c0251edec", "guid": "bfdfe7dc352907fc980b868725387e989d1770b9a2f6b37f93a514cb958e29ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f832a65a6c1439cb75fa0e12f1a95b6f", "guid": "bfdfe7dc352907fc980b868725387e98327d8d630b70bdfd6daa649feec67bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfad50fda6afa98ca32858b3ab0b3dce", "guid": "bfdfe7dc352907fc980b868725387e983ee5b469a86f27b8e2c7aca3d4570043"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a1b2f390ec060639eee52dddda1ded7", "guid": "bfdfe7dc352907fc980b868725387e9878c1eccc3cc4fbc3910550194fe42f0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0235a5765c27ac88ee50bc190e72e0", "guid": "bfdfe7dc352907fc980b868725387e9878a216d321f64171005b4a39d23418fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f79bcfe2d14d71e6383e7e8efac4c36", "guid": "bfdfe7dc352907fc980b868725387e98221fdd9b392796c927bbb85166767248"}], "guid": "bfdfe7dc352907fc980b868725387e98a4aa9c79cd0ad589e5986c6a793c7569", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e982f53c31d49d3039187aca0202c785aa8"}], "guid": "bfdfe7dc352907fc980b868725387e985a87658fd90a1d7db61dbb5b1b82aefa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9811a6ce420babe3491436f103a769ee18", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}], "guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988d4f056f23e4e16df108000d3c5e64e7", "name": "GoogleMapsUtils.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bbb8a9f2908ccb16a5669503da3350d1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986a4b892f420bab35cd2a623b790b8f7c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808d5923f2056835192e8ae0f75026ad5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bdfb8049ef36ddc5da68f170463f24d6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808d5923f2056835192e8ae0f75026ad5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c343f21e53e555651103f1c1657a9cdf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf07f516144fdd964ccbaa32bfee928f", "guid": "bfdfe7dc352907fc980b868725387e98c49064fce69217ff98145931604c6359", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd03c05b03cb8ea7cdf7d49443a8790", "guid": "bfdfe7dc352907fc980b868725387e98871d3118b2cf06ce39619fa0eb297518", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13cc343645a4c4a8723836e182e8c7b", "guid": "bfdfe7dc352907fc980b868725387e9882c5c096e2902ae717f48ed4b489f4a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824de7bf9d3983914c90378e1ccf725ea", "guid": "bfdfe7dc352907fc980b868725387e98a0ad6460bee0831e17b9ab284870a9a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bff5997e882e8351916a1470d0c4564", "guid": "bfdfe7dc352907fc980b868725387e98f898e2c6bab3151b08e8664917ecaa64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d443d94e3b0f83e24fd777989dbbc852", "guid": "bfdfe7dc352907fc980b868725387e9860d72ebe0347070603ef310843b892ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4151da1941c6d83484ad2c3206aea16", "guid": "bfdfe7dc352907fc980b868725387e982fb58888381d49ca3cc1b4d5f8f834f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28087c9e505a8aa23bf57d6b34643d3", "guid": "bfdfe7dc352907fc980b868725387e984f28964bc1cec12762c4f11a2a3f6e44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dce96620652bafd898ab6e2e307c915", "guid": "bfdfe7dc352907fc980b868725387e984e106561f36d20aa53c7a644feb2eb02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817203d2ef34142d6d74046d55dbfd2fb", "guid": "bfdfe7dc352907fc980b868725387e98cf4db2cd7dc1e8861b86ca897e7fea7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988951d0b55186a56c60dcf9dcb9ddc82a", "guid": "bfdfe7dc352907fc980b868725387e98720831903f0b06f92ed7ad557c25304e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff3820999ec12a7db0cf3a8c4f55be3", "guid": "bfdfe7dc352907fc980b868725387e9891f2d572cf8ccc845295051b8f3f6886", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98598bb75d9f254478b77e0873dfc6ffd1", "guid": "bfdfe7dc352907fc980b868725387e98a123acbfbeb117777ac465df9ccaf14f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345449fa4072399a6253833d3de405d8", "guid": "bfdfe7dc352907fc980b868725387e98356bd0d4a8837819c7785d3000a251ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f9e9e6a9810de32b23a9c276492960", "guid": "bfdfe7dc352907fc980b868725387e9875c619823c28e978bd8efb2a6487a39a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b6bdce9fbd377335ebbc285511e5bfd", "guid": "bfdfe7dc352907fc980b868725387e98d8cae0b4ace9bea6f79cbdacf6df724e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6a0ba2ddc7887d740ab01532fb0caa5", "guid": "bfdfe7dc352907fc980b868725387e988baeea8035a60b23828c5a028c2f0b57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e287e32f78a316fef056e2d71cadae", "guid": "bfdfe7dc352907fc980b868725387e98fc9cfcbc21c0e90e5cc87307d66362f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835f1bf1b1b1e444d931f98824af7f3fa", "guid": "bfdfe7dc352907fc980b868725387e983e9208316d394cde652626623b0cdf84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7820ce811cad8bd315e5117679d6312", "guid": "bfdfe7dc352907fc980b868725387e98a343546a41824e93b6d403ec5310f725", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828d619024d60355045198dad7d13bfc9", "guid": "bfdfe7dc352907fc980b868725387e981aef3c2a0f140a99629c8928d734b916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809964c30f05d00cde67aa662af1626b1", "guid": "bfdfe7dc352907fc980b868725387e981243c693a203d3ee6be5f572306085f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1cd19cb6da485c9f56d05a672e5cc20", "guid": "bfdfe7dc352907fc980b868725387e98412bd3c9534adcbb7a8b1cecb6c01b2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985786b7d05930b512791eaa0a97278da2", "guid": "bfdfe7dc352907fc980b868725387e98d10ef293f13d23d2ac1e4252e08e0f3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e7caa55e314b1e772ffc5513b8d9023", "guid": "bfdfe7dc352907fc980b868725387e98cf117e5cb8c79bf9fbaeff6e1505c4ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c4e5aac4ebc143b18b6ad9fdcdbcd8", "guid": "bfdfe7dc352907fc980b868725387e98ff7a525a1bf90747da19464afa374a81"}], "guid": "bfdfe7dc352907fc980b868725387e98e601aadb7145b7c9c6fc674d6d0783c5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f0ff927e31de690b562d742c35a04952", "guid": "bfdfe7dc352907fc980b868725387e982774a955cca7c0e636f0ae4ced75b916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ecfc930fef1057c6ab64a9bc0d576e", "guid": "bfdfe7dc352907fc980b868725387e98ad2de61fc58c7ab06c2d9816842d7bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c27ef2284bada3178a499187737f1bae", "guid": "bfdfe7dc352907fc980b868725387e989a9f926b485483637a4d5b406b5c5865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b1cfb6077130cb8ec388164e6824153", "guid": "bfdfe7dc352907fc980b868725387e98ed56b1beac352336ce2b5de784d7b0b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d70df0567b7b8d3fef7bf8683a408c49", "guid": "bfdfe7dc352907fc980b868725387e980abf6e975d4c482fcb0bf6acf3e6d740"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2b42bef276761a8c8590081bfae1a9", "guid": "bfdfe7dc352907fc980b868725387e98e1cccbcc193154671e2ab12697394b4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d816f2622f4863635b0483027d84d49e", "guid": "bfdfe7dc352907fc980b868725387e989da16b64ff89a7e728ad8e4645ccbc0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818865f4cec7494103a34bc0727edd33f", "guid": "bfdfe7dc352907fc980b868725387e98a9dd3e4f394d828acaae985d907a6008"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879300f85cbc276809b5c577bc4ee1ad6", "guid": "bfdfe7dc352907fc980b868725387e988e9966b4bb12885598a67d6b89034ccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c07f7573b2bfa2a08809b65b6892255", "guid": "bfdfe7dc352907fc980b868725387e9826e1b62083bd3bbc4b8334f8386597f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98540505e4c6c928c81e8e36f98fe82211", "guid": "bfdfe7dc352907fc980b868725387e981b523f2a06dbc8823ba04510797226d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de806e0fa57c5b9e6b949687b23e1c74", "guid": "bfdfe7dc352907fc980b868725387e982c79e4f9feadcc1a0afcdd151cd7fa08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f39bf94fabc0ef09108922b302529822", "guid": "bfdfe7dc352907fc980b868725387e989f2b801aa096272bebc6e8294dc9e343"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3613dc3e2c771d062207071d0cbd45f", "guid": "bfdfe7dc352907fc980b868725387e98e0aeecbce0f7354401dc7b7d5abed264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b7e96c4a57f2ce30a3384313998fc8", "guid": "bfdfe7dc352907fc980b868725387e988410be078285786dcadb0a56fdf3e4c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830a940b45c68c90745493f0224db3233", "guid": "bfdfe7dc352907fc980b868725387e989fab6664f52bb58b44128558530d1279"}], "guid": "bfdfe7dc352907fc980b868725387e986f64769cc32ff876c34f8b64b831dd68", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e983e3112697bafd510f0522cb2212cb5ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e98f6df1dc827d9df23291801ff3e8d46a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f0d1dd6b952d008563ab75cee403874", "guid": "bfdfe7dc352907fc980b868725387e9846b5e039be2bfc7e3dbbeb7d1fe26f70"}], "guid": "bfdfe7dc352907fc980b868725387e98ba7a841adc5fe60210dbf914cba44b11", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9805f266c7bb43d645f62f58eb1e86298a", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98b0e7889a0562c9bcf0f01408f7007101", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
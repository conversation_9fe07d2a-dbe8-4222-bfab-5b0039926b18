{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b04e6d4bde46472a635b058fabcb5059", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98557de503f5162ce03c72904d5c6be598", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ad62e6dd64f4435461867290707327d2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c526f01fb16e81c522a0d7df8d08d6c8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ad62e6dd64f4435461867290707327d2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d6396bcd625384e52044bd40e1b0a0b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98131e49f547fb854bc990a0f9962cc886", "guid": "bfdfe7dc352907fc980b868725387e986951714f811931d6e7f0d80ea36f4033", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98729763aecdfce2ebe0b7dae0142d29b0", "guid": "bfdfe7dc352907fc980b868725387e9818f542e65448ea9662756d6faf1f0bf9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989427c66fd5c693f93de135295d25e56a", "guid": "bfdfe7dc352907fc980b868725387e9867f78b077ede4b8e45c5a0bffb31329c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6380fcda1faae5ef4770fe540fba50d", "guid": "bfdfe7dc352907fc980b868725387e987eb469e44c37277d40fdb3bad795da28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988454ea075c7b2812b3f8738427a44ac9", "guid": "bfdfe7dc352907fc980b868725387e98021001bcc4e4065621d86c41b4959564", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824e5f7342d1473d9ae338296e8bce73b", "guid": "bfdfe7dc352907fc980b868725387e98add11df3a338c94dbbf2f7f1fce6d3d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc5c82fa7bd7bc5be9a8b71368bae315", "guid": "bfdfe7dc352907fc980b868725387e98c0f9828c73ea75828518fff481eeec0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d99a4fc2208062c18cf39e237b2adc", "guid": "bfdfe7dc352907fc980b868725387e9822dc2d709dea585d6f184c6dbcc6bafd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b48143ed2e6a9ddc3c6653742bbaab7b", "guid": "bfdfe7dc352907fc980b868725387e983effc163005db912dc23901a100e2f68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fb00d610c736a5a78ce7cb197440e85", "guid": "bfdfe7dc352907fc980b868725387e98f2a41313c5b33d932ab892e31cdbeb98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986010e57683e8c2d32409c808c46170f0", "guid": "bfdfe7dc352907fc980b868725387e9801505937f81a7b566f87d44d744bba51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28e411f4aee563894628dd890723408", "guid": "bfdfe7dc352907fc980b868725387e9813b833ab39b3d589c8eb0d86525aafe4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838239098db16adbebb41322a94f1c068", "guid": "bfdfe7dc352907fc980b868725387e98ba5366f207af0f9ca38478f8629a0723", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98775c0346941ed7f4af03b98e77826304", "guid": "bfdfe7dc352907fc980b868725387e98c637efd554b7fc574d121ad583099b8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29b4e37e7afa05fb1d432a6bab029b0", "guid": "bfdfe7dc352907fc980b868725387e98180494377abf7c34e6cb55a472fced62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fc0ba8e952daccac49b2f92676b5bdf", "guid": "bfdfe7dc352907fc980b868725387e982f2df65e537dac91286016ddc8baf8c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850163aedb90146c2dc7358af9c44b145", "guid": "bfdfe7dc352907fc980b868725387e98ee9169788874e8a0423ceda251d2bbe5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841fce8ea10728cb4755a886df1f6b8d5", "guid": "bfdfe7dc352907fc980b868725387e98eecfe1429069e2e6038f4212a40d56d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fda8a814db5300b8ece78bbb90c4b78d", "guid": "bfdfe7dc352907fc980b868725387e980f6ab4644b029d5798cd39ae3efd0048", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d0fe815125037263fca7e62fb49800", "guid": "bfdfe7dc352907fc980b868725387e98efa74dc171d50707fa359774258d052a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e74d15bf678b9f667fd7b1d4a06fd30d", "guid": "bfdfe7dc352907fc980b868725387e98c8c0271eaea39ecdedb853c7ce4a007c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e49797b82db194f5508c061377e6e6c", "guid": "bfdfe7dc352907fc980b868725387e98255913acd5b83ac5aff9c2f90c3dddda", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d2d0b2d2b7372ebdefb6dee423e6b9e0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b56aa3e676083b537337b67fd236255f", "guid": "bfdfe7dc352907fc980b868725387e98e950d2c3e87f46887fc0bed6b38e979e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9421014c9fb404f6b1246b5576af72", "guid": "bfdfe7dc352907fc980b868725387e98ba08493a15e14358ba225a8f8bd6d3c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a3d9016bb6b845995e474bdda6b8ab1", "guid": "bfdfe7dc352907fc980b868725387e98658d32953e2d0e56f080924d19d10756"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf6f4d5bfcb721e58bbca37a68c187d", "guid": "bfdfe7dc352907fc980b868725387e9870c5a4670664f1453bd9daf896371d28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ede6ee848d6427df421a96b86fa5dde6", "guid": "bfdfe7dc352907fc980b868725387e9817ea1a1b27588a72b010adc1baa3fb9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873787769b0fc696a9843e58b31a85476", "guid": "bfdfe7dc352907fc980b868725387e9801dde30d9fafeb1db6759b280751cd15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986368e74c0dc47726287ac0ff8b0ba112", "guid": "bfdfe7dc352907fc980b868725387e985baf79912d2cbef6b646fc508718d886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92b5b5338a9b2cb7dc7d8361c80c8f3", "guid": "bfdfe7dc352907fc980b868725387e9817303310c40e9ef65879bcdc8919cb3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981caff4a2fa9f26fb57cf8cb5b7abca65", "guid": "bfdfe7dc352907fc980b868725387e9883aefe29e27a3bc3a6bc00221d52325a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f7217f4c4ea8c601c46b373238eed4", "guid": "bfdfe7dc352907fc980b868725387e985f44209f09fde43e531893db86d727e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c386c937ac295d9f7d67d3491b314cd4", "guid": "bfdfe7dc352907fc980b868725387e9858a89946d63501cf971d8a89c3eb062e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f0cf8c6e8bd1dc2e4cd3179bd00c7c2", "guid": "bfdfe7dc352907fc980b868725387e9886d17233b8c5637d70c31a742416b2c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98347cf9bab1d92ec6aca1b5e9fa82f61c", "guid": "bfdfe7dc352907fc980b868725387e9898f55d4169c2d63d17f9d8b9642b2bfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982533fd392c7cfcaec67d8e3a4731eda9", "guid": "bfdfe7dc352907fc980b868725387e98893d0a71773540cf9dfa227f4ceef71d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a58d017b53fa88aab8427b2266910759", "guid": "bfdfe7dc352907fc980b868725387e98652a87b9dfd4ffa3ae044fc39bc45569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee968266e79acc16cd9f52b45707d057", "guid": "bfdfe7dc352907fc980b868725387e986e361bd4246cfa3e112cb601d4bbd323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7bb63d282ce1deee935e419630c2504", "guid": "bfdfe7dc352907fc980b868725387e98b2b56a30ce31749ddfc9ffeed9f46104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1176c447db097cebe865e90ce77ae1b", "guid": "bfdfe7dc352907fc980b868725387e981aee11a376fe48759c4e8fec77c85bf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d8637ed3dbb074d4c280793a0cc9f4e", "guid": "bfdfe7dc352907fc980b868725387e98c8de8c100ef64e316a253335aaedc120"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197e9da3b8a98f06c5338afa2575d051", "guid": "bfdfe7dc352907fc980b868725387e9870568cc41046e38ce47061eee881215f"}], "guid": "bfdfe7dc352907fc980b868725387e983d00a31dcc171689f12dd8a2f6dabb93", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e981308508a16b29834a9442a8631a109af"}], "guid": "bfdfe7dc352907fc980b868725387e986ac99927ff05a96521fa79555e84ccb3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f14e735e3487184f654f3d63c7709a43", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e986bd98f1f26d3876769b2e753e40abbfd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
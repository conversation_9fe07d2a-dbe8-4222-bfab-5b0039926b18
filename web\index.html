<!DOCTYPE html>
<html lang="en">
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base
  -->
  <base href="/">
  <meta name="google-signin-client_id" content="YOUR_GOOGLE_SIGN_IN_CLIENT_KEY">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A Ecommerce Website.">

  <!-- DEMO ONLY -->
<!--  <meta name="robots" content="nofollow, noindex, max-snippet:1, max-video-preview:1, max-image-preview:standard">-->
<!--  <link rel="canonical" href="https://hexacom-web.6amtech.com"/>-->




  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="eMarket">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">


  <title>Hexacom</title>
  <link rel="manifest" href="manifest.json">
  <link rel="stylesheet" type="text/css" href="style.css">

  <link rel="shortcut icon" type="image/png" href="favicon.png"/>




  <!-- // Other stuff -->

  <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_MAP_KEY_HERE=async&callback=Function.prototype"></script>

  <script type="application/javascript" src="/assets/packages/flutter_inappwebview_web/assets/web/web_support.js" defer></script>

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = {{flutter_service_worker_version}};
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js?version=7.7" defer></script>


</head>
<body>


<div class="preloader">
  <div class="preloader-container">
    <img width="55" class="svg preloader-img" src="assets/preloader.svg" alt="">
    <div class="loader"></div>
  </div>
</div>
<!-- Header -->
<header class="header">
  <div class="header-top-line"></div>
  <div class="header-container d-flex align-items-center justify-content-between">
    <div class="header-start d-flex gap-4 align-items-center">
      <img class="logo" src="assets/logo.svg" alt="">
      <div class="placeholder"></div>
      <div class="placeholder"></div>
      <div class="placeholder"></div>
    </div>
    <div class="header-end d-flex align-items-center gap-4">
      <div class="placeholder placeholder-wide"></div>
      <div class="placeholder"></div>
      <div class="placeholder"></div>
      <img src="assets/cart_icon.svg" alt="">
      <img src="assets/menu_icon.svg" alt="">
    </div>
  </div>
</header>

<script>
  // Check if localStorage is supported in the browser
  if (typeof Storage !== "undefined") {
    var itemValue = localStorage.getItem("flutter.theme");
    document.body.classList.toggle("theme-dark", itemValue === "true");
  }
</script>

<script>
  if ("serviceWorker" in navigator) {
    window.addEventListener("load", function () {
      // navigator.serviceWorker.register("/flutter_service_worker.js");
      navigator.serviceWorker.register("/firebase-messaging-sw.js");
    });
  }
</script>

<script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>


<script>
  window.addEventListener('load', function(ev) {
    {{flutter_js}}
    {{flutter_build_config}}

    _flutter.loader.load({
      serviceWorker: {
        serviceWorkerVersion: {{flutter_service_worker_version}},
      },
      onEntrypointLoaded: function(engineInitializer) {
        let config = { renderer: 'html' };
        engineInitializer.initializeEngine(config).then(function(appRunner) {
          appRunner.runApp();
        });
      }
    });
  });
</script>


<script>
  // Your web app's Firebase configuration
  // For Firebase JS SDK v7.20.0 an later, measurementId is optional
  var firebaseConfig = {
    apiKey: "AIzaSyBCtDfdfPqxXDO6rDNlmQC1VJSHOtuyo3w",
    authDomain: "gem-b5006.firebaseapp.com",
    projectId: "gem-b5006",
    storageBucket: "gem-b5006.firebasestorage.app",
    messagingSenderId: "384321080318",
    appId: "1:384321080318:web:9cf2ec90f41dfb8a2c0eaf",
  };
  // Initialize Firebasefi
  firebase.initializeApp(firebaseConfig);
</script>
</body>
</html>
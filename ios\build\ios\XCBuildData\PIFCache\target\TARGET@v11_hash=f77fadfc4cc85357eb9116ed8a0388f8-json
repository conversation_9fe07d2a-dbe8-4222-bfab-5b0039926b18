{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4704530abb5248010ddf4a263694582", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98789df9d16830914da249d17865a7be16", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f26252526a0de9833e491c64085e109", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ebd2189f81760dc5002519fef09537d1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f26252526a0de9833e491c64085e109", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989806f6a9767e4ffc5e1ceb6ae83f3aa1", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984a25595f71376cad480e2e6ddf1f8167", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98013c55a4c8c97644588958c1faae4053", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98afc87963ad324fc64ee8a3914a3f2436", "guid": "bfdfe7dc352907fc980b868725387e98316e61667647eb89b2dcd75c33ebc3d4"}], "guid": "bfdfe7dc352907fc980b868725387e98a5dc965e1e8b370bbb411c5b5442bbc3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e989f9161ff8e5b778c00c0f4202dcaadc9", "name": "pointer_interceptor_ios-pointer_interceptor_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f49a561b1f656669a5bf019886c6571e", "name": "pointer_interceptor_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
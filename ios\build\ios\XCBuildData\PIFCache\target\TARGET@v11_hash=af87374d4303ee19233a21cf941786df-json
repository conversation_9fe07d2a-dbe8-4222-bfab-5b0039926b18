{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980d3b4b92bc1d3635986057a5e3d87407", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983acee0263ee80cf24be6e20e3fe1d937", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b24aa374c0279424fa614d506effaae", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3c1f856451a44dd2238bb8e03f629a8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b24aa374c0279424fa614d506effaae", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981496f826cc976bf124c3e2cdf403a9f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984bab3cd7a86752799c98de5b216699ff", "guid": "bfdfe7dc352907fc980b868725387e984b4e2b2d38ceb40598d0629a6a29a998", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1eb555404e6c773b71f943a01ce8e65", "guid": "bfdfe7dc352907fc980b868725387e983e0f4f5b863174fcb32043e87b8e66a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e97d58c0a998fb54b005682d2b674ac", "guid": "bfdfe7dc352907fc980b868725387e98dc580796952acfb7fbf0fded35f54e67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb594444100ccbe200e6b5b727d5560e", "guid": "bfdfe7dc352907fc980b868725387e98d22586e69bf2aef8b039891bcc650665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841571bbf1ac10295f343b905847e47de", "guid": "bfdfe7dc352907fc980b868725387e9865ebbb9e2407b80c90fed1db89b340e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb389a9afb1db912b7419c59fdac4c42", "guid": "bfdfe7dc352907fc980b868725387e9899cdbb3f5c2a220155a76e55140322ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845fae101707aa1e9a09f08b9fc3f7a63", "guid": "bfdfe7dc352907fc980b868725387e98fd3712aa9d01e1fc775bfabbb3600693", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830d7c5e13a3b44a238dccb4dfa062613", "guid": "bfdfe7dc352907fc980b868725387e98850ddc5ee2cbcd07af827524c7dfb78e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873aee38e1048bc14d13a4ae61f9474d5", "guid": "bfdfe7dc352907fc980b868725387e98036e607cec0f7f2b6292d3448d94d705", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899725c7f97278dc776841639aa246cf5", "guid": "bfdfe7dc352907fc980b868725387e98fa7d18e838630224541f4a32f8fe3d79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98620d5d6d03dc5708da9c8670e9b63980", "guid": "bfdfe7dc352907fc980b868725387e98d20f516538f062edb154dff2e9e72326", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceb0e4035889e5b0293c0b8ed98c91c2", "guid": "bfdfe7dc352907fc980b868725387e9863adc356814f9a68bae9d10e183d3dbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22a84d276f47e105850738ae214865d", "guid": "bfdfe7dc352907fc980b868725387e98a562d3226230ecc9e150571e6a8ca86a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad7ec460f9e00d0a1006e59b8dc3e286", "guid": "bfdfe7dc352907fc980b868725387e986a89fa83a23fb0ba50e0aaa9890b3cb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986adc0ae85f0d7495b07877dfe701e8cc", "guid": "bfdfe7dc352907fc980b868725387e98c7a4d2f624029d7e36489918cfb486d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881b5c5e120ae2207c6aa384d4dd42e6b", "guid": "bfdfe7dc352907fc980b868725387e98840c991519783c8bc41b731ef77fbf43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a67c807426152ca9504eb877a77b4cb3", "guid": "bfdfe7dc352907fc980b868725387e98b6ea7a37cc9b1b085cc626894408a980", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bf564ff1975ff472a8bc2c91f2bfcf9f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983d2a71f8fdec9af261288f89cc66f115", "guid": "bfdfe7dc352907fc980b868725387e98fc2aeeff30372a8e9382bd9424da5e58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2103681fbaeb0b7970692abb5d1564f", "guid": "bfdfe7dc352907fc980b868725387e98ba368f5b46b86ed2f9356bbb228ec697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c89d4f9a04b54998b49fff7e30b9fca3", "guid": "bfdfe7dc352907fc980b868725387e98a07f46006e0e0b13acf0b1712cc73130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850b9d299c7b571892f919c370ec7f562", "guid": "bfdfe7dc352907fc980b868725387e982608d167b16e3c511c909caa418e1af1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495e000a6851bb3f57ee8e8badf73806", "guid": "bfdfe7dc352907fc980b868725387e98185a926dec138453c5fabdc29d0c9a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98194311cef28f8df7959444f755bdae41", "guid": "bfdfe7dc352907fc980b868725387e9881210550520b1ee3bfa30fce3d413653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4bccd711323441cfd85e1ab76372573", "guid": "bfdfe7dc352907fc980b868725387e989db863b76450c07e6a3efeefea5d239e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1ecbbde17590f1cf88177897384155", "guid": "bfdfe7dc352907fc980b868725387e986da211b77f3869a367c7cc59fd32c453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98857e8e4b7407875f0070152327d9148a", "guid": "bfdfe7dc352907fc980b868725387e981d7eb2fc908b56d891d52aed5a4a5892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e099a6c4a92fc8878a83beb1d03ba6e6", "guid": "bfdfe7dc352907fc980b868725387e98f50096c22f8337a29ee522ddda53a055"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e93ff172fadf840903fbe602b54c3a3", "guid": "bfdfe7dc352907fc980b868725387e9842aab6e6820437225615089c83902796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e374e23dd670863e0c0fe32ae5211b6", "guid": "bfdfe7dc352907fc980b868725387e9857aea391777027b04b5707666b521569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cbb155282eaf110405694ba7541c4ee", "guid": "bfdfe7dc352907fc980b868725387e9881ead7d5ee06c80b6d2e153309173273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887285ad859d865d59c8ebb41ae0b039f", "guid": "bfdfe7dc352907fc980b868725387e98eaaa226cb9428e050cb7f41b2f6c2bd1"}], "guid": "bfdfe7dc352907fc980b868725387e981b2b4abaaa263ff11fa795c21ec7f9d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e9850046155a8d9eab9c34f139cf05eb31e"}], "guid": "bfdfe7dc352907fc980b868725387e9800faf1016cc785048ac6ced913c4cff8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc5dfdf9349a221f5258db13a60fd150", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98651b31e0272f628eba1fc39908fbac59", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}